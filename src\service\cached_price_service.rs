use std::sync::Arc;
use std::time::Duration;
use mongodb::{bson::doc, Collection, Database};
use tokio::time::{timeout, sleep, Instant};
use tokio::sync::RwLock;
use futures::stream::TryStreamExt;
use tracing::{info, warn, error, debug};
use anyhow::{Result, Context};

use crate::model::{CachedPrice, Blockchain, BotError};
use crate::service::{db_service::DbService, price_service::PriceService};

const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(10);
const PRICE_CACHE_TTL: u64 = 300; // 5 minutes in seconds (reduced from 1 hour)
const BACKGROUND_UPDATE_INTERVAL: u64 = 1800; // 30 minutes in seconds (reduced from 1 hour)
const API_RATE_LIMIT_DELAY: Duration = Duration::from_millis(2000); // 2 seconds between API calls

#[derive(Debug, Clone)]
pub struct RateLimiter {
    last_request: Arc<RwLock<Instant>>,
    min_interval: Duration,
}

impl RateLimiter {
    pub fn new(min_interval: Duration) -> Self {
        Self {
            last_request: Arc::new(RwLock::new(Instant::now() - min_interval)),
            min_interval,
        }
    }

    pub async fn wait_if_needed(&self) {
        let mut last_request = self.last_request.write().await;
        let now = Instant::now();
        let elapsed = now.duration_since(*last_request);

        if elapsed < self.min_interval {
            let wait_time = self.min_interval - elapsed;
            debug!("Rate limiting: waiting {:?}", wait_time);
            sleep(wait_time).await;
        }

        *last_request = Instant::now();
    }
}

#[derive(Debug, Clone)]
pub struct CachedPriceService {
    pub db: Database,
    pub price_service: Arc<PriceService>,
    pub rate_limiter: Arc<RateLimiter>,
}

impl CachedPriceService {
    pub fn new() -> Self {
        Self {
            db: DbService::get_db().clone(),
            price_service: Arc::new(PriceService::new()),
            rate_limiter: Arc::new(RateLimiter::new(API_RATE_LIMIT_DELAY)),
        }
    }

    /// Get USD value for a blockchain's native token amount using cached prices
    pub async fn get_usd_value(&self, blockchain: &Blockchain, amount: f64) -> Result<f64, BotError> {
        let native_symbol = self.get_native_symbol(blockchain);
        let price_usd = self.get_cached_price(blockchain).await?;
        Ok(amount * price_usd)
    }

    /// Get cached price for a blockchain, fetch if not available or stale
    pub async fn get_cached_price(&self, blockchain: &Blockchain) -> Result<f64, BotError> {
        let collection: Collection<CachedPrice> = self.db.collection("cached_prices");
        let native_symbol = self.get_native_symbol(blockchain);

        // Try to get cached price first
        let filter = doc! {
            "blockchain": format!("{:?}", blockchain),
            "native_symbol": &native_symbol
        };

        let cached_price_result = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find_one(filter.clone(), None)
        ).await;

        match cached_price_result {
            Ok(Ok(Some(cached_price))) => {
                // Check if price is still fresh (less than 1 hour old)
                if !cached_price.is_stale(PRICE_CACHE_TTL) {
                    debug!("Using cached price for {:?}: ${:.4} (age: {}s)", 
                           blockchain, cached_price.price_usd, cached_price.age_seconds());
                    return Ok(cached_price.price_usd);
                } else {
                    info!("Cached price for {:?} is stale (age: {}s), fetching fresh price", 
                          blockchain, cached_price.age_seconds());
                }
            }
            Ok(Ok(None)) => {
                info!("No cached price found for {:?}, fetching fresh price", blockchain);
            }
            Ok(Err(e)) => {
                warn!("Database error getting cached price for {:?}: {}", blockchain, e);
            }
            Err(_) => {
                warn!("Timeout getting cached price for {:?}", blockchain);
            }
        }

        // Fetch fresh price and update cache
        self.fetch_and_cache_price(blockchain).await
    }

    /// Fetch fresh price from API and update cache with rate limiting
    async fn fetch_and_cache_price(&self, blockchain: &Blockchain) -> Result<f64, BotError> {
        let native_symbol = self.get_native_symbol(blockchain);

        // Apply rate limiting before making API call
        self.rate_limiter.wait_if_needed().await;

        info!("Fetching fresh price for {:?} with rate limiting...", blockchain);

        // Fetch fresh price using the existing price service with retry logic
        let fresh_price = match self.fetch_price_with_retry(blockchain, 3).await {
            Ok(price) => price,
            Err(e) => {
                error!("Failed to fetch price for {:?} after retries: {}", blockchain, e);
                // Return a fallback price to prevent complete failure
                let fallback_price = self.get_fallback_price(blockchain);
                warn!("Using fallback price for {:?}: ${:.4}", blockchain, fallback_price);
                fallback_price
            }
        };

        info!("Fetched fresh price for {:?}: ${:.4}", blockchain, fresh_price);

        // Update cache in database
        if let Err(e) = self.update_cached_price(blockchain, &native_symbol, fresh_price).await {
            warn!("Failed to update cached price for {:?}: {}", blockchain, e);
            // Don't fail the request if cache update fails, just return the fresh price
        }

        Ok(fresh_price)
    }

    /// Fetch price with retry logic for handling rate limits
    async fn fetch_price_with_retry(&self, blockchain: &Blockchain, max_retries: u32) -> Result<f64, BotError> {
        let mut last_error = None;

        for attempt in 1..=max_retries {
            match self.price_service.get_native_token_price(blockchain).await {
                Ok(price) => return Ok(price),
                Err(e) => {
                    last_error = Some(e.clone());
                    if attempt < max_retries {
                        let delay = Duration::from_secs(2_u64.pow(attempt)); // Exponential backoff
                        warn!("Price fetch attempt {} failed for {:?}: {}. Retrying in {:?}...",
                              attempt, blockchain, e, delay);
                        sleep(delay).await;
                    }
                }
            }
        }

        Err(BotError::general_error(format!(
            "Failed to fetch price for {:?} after {} attempts: {}",
            blockchain, max_retries, last_error.unwrap_or_else(|| "Unknown error".into())
        )))
    }

    /// Get fallback price when API fails
    fn get_fallback_price(&self, blockchain: &Blockchain) -> f64 {
        // Approximate fallback prices to prevent complete failure
        match blockchain {
            Blockchain::SOL => 100.0,
            Blockchain::ETH => 3000.0,
            Blockchain::BSC => 400.0,
            Blockchain::BASE => 3000.0, // BASE uses ETH
        }
    }

    /// Update cached price in database
    async fn update_cached_price(&self, blockchain: &Blockchain, native_symbol: &str, price_usd: f64) -> Result<(), BotError> {
        let collection: Collection<CachedPrice> = self.db.collection("cached_prices");
        
        let filter = doc! {
            "blockchain": format!("{:?}", blockchain),
            "native_symbol": native_symbol
        };

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let update_doc = doc! {
            "$set": {
                "price_usd": price_usd,
                "last_updated": now as i64
            },
            "$setOnInsert": {
                "blockchain": format!("{:?}", blockchain),
                "native_symbol": native_symbol,
                "created_at": now as i64
            }
        };

        let options = mongodb::options::UpdateOptions::builder()
            .upsert(true)
            .build();

        timeout(
            DB_OPERATION_TIMEOUT,
            collection.update_one(filter, update_doc, options)
        ).await
        .map_err(|_| BotError::database_error("Timeout updating cached price".to_string()))?
        .map_err(|e| BotError::database_error(format!("Failed to update cached price: {}", e)))?;

        debug!("Updated cached price for {:?}: ${:.4}", blockchain, price_usd);
        Ok(())
    }

    /// Initialize cache with current prices for all supported blockchains
    pub async fn initialize_cache(&self) -> Result<(), BotError> {
        info!("Initializing price cache for all supported blockchains...");

        let blockchains = vec![
            Blockchain::SOL,
            Blockchain::ETH,
            Blockchain::BSC,
            Blockchain::BASE,
        ];

        for blockchain in blockchains {
            match self.fetch_and_cache_price(&blockchain).await {
                Ok(price) => {
                    info!("✅ Initialized cache for {:?}: ${:.4}", blockchain, price);
                }
                Err(e) => {
                    error!("❌ Failed to initialize cache for {:?}: {}", blockchain, e);
                    // Continue with other blockchains even if one fails
                }
            }
        }

        info!("Price cache initialization completed");
        Ok(())
    }

    /// Background task to update all cached prices every hour
    pub async fn start_background_price_updater(&self) {
        info!("Starting background price updater (interval: {}s)", BACKGROUND_UPDATE_INTERVAL);
        
        let mut interval = tokio::time::interval(Duration::from_secs(BACKGROUND_UPDATE_INTERVAL));
        let service = self.clone();

        tokio::spawn(async move {
            loop {
                interval.tick().await;
                
                info!("🔄 Background price update started");
                
                let blockchains = vec![
                    Blockchain::SOL,
                    Blockchain::ETH,
                    Blockchain::BSC,
                    Blockchain::BASE,
                ];

                for blockchain in blockchains {
                    match service.fetch_and_cache_price(&blockchain).await {
                        Ok(price) => {
                            debug!("✅ Updated background cache for {:?}: ${:.4}", blockchain, price);
                        }
                        Err(e) => {
                            warn!("⚠️ Failed to update background cache for {:?}: {}", blockchain, e);
                        }
                    }

                    // Longer delay between API calls to avoid rate limiting (rate limiter handles this too)
                    tokio::time::sleep(Duration::from_secs(5)).await;
                }
                
                info!("🔄 Background price update completed");
            }
        });
    }

    /// Get all cached prices for debugging/monitoring
    pub async fn get_all_cached_prices(&self) -> Result<Vec<CachedPrice>, BotError> {
        let collection: Collection<CachedPrice> = self.db.collection("cached_prices");
        
        let cursor_result = timeout(
            DB_OPERATION_TIMEOUT,
            collection.find(doc! {}, None)
        ).await;

        match cursor_result {
            Ok(Ok(mut cursor)) => {
                let mut prices = Vec::new();
                while let Ok(Some(price)) = cursor.try_next().await {
                    prices.push(price);
                }
                Ok(prices)
            }
            Ok(Err(e)) => Err(BotError::database_error(format!("Failed to get cached prices: {}", e))),
            Err(_) => Err(BotError::database_error("Timeout getting cached prices".to_string())),
        }
    }

    /// Helper to get native symbol for blockchain
    fn get_native_symbol(&self, blockchain: &Blockchain) -> String {
        match blockchain {
            Blockchain::SOL => "SOL".to_string(),
            Blockchain::ETH => "ETH".to_string(),
            Blockchain::BSC => "BNB".to_string(),
            Blockchain::BASE => "ETH".to_string(),
        }
    }
}
