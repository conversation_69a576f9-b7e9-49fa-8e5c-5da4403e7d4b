use teloxide::types::{Inline<PERSON>eyboardButton, InlineKeyboardMarkup};
use teloxide::prelude::Requester;
use teloxide::payloads::EditMessageTextSetters;
use crate::model::{UserData, BotError, Blockchain};
use crate::service::{BotService, TokenInfoService};


pub async fn show_bsc_dashboard(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
) -> Result<(), BotError> {
    let (chat_id, wallet_address, initial_dashboard_text, keyboard, config_text) = {
        let wallet_address = &user_data.get_wallet(&Blockchain::BSC).address;
        let chat_id = user_data.chat_id();

        const DASHBOARD_HEADER: &str = "📊 <b>BSC Dashboard</b>\n\n";
        const BLOCKCHAIN_INFO: &str = "🔗 <b>Blockchain:</b> BSC (Binance Smart Chain)\n\n";
        const LOADING_BALANCE: &str = "💰 <b>Balance:</b> <code>Loading...</code>\n\n";
        const NETWORK_STATUS: &str = "📈 <b>Network Status:</b> Active\n\n";

        let mut text = String::with_capacity(512);
        text.push_str(DASHBOARD_HEADER);

        use std::fmt::Write;
        let _ = write!(text, "👤 <b>User:</b> {}\n\n", user_data.display_name());
        let _ = write!(text, "📍 <b>Address:</b> <code>{}</code>\n\n", wallet_address);

        text.push_str(BLOCKCHAIN_INFO);
        text.push_str(LOADING_BALANCE);
        text.push_str(NETWORK_STATUS);

        let config = format_config(user_data, Blockchain::BSC);
        let _ = write!(text, "⚙️ <b>Settings:</b> {}", config);

        let kb = create_dashboard_keyboard(Blockchain::BSC);

        (chat_id, wallet_address.clone(), text, kb, config)
    };

    bot_service.edit_message_with_keyboard(
        chat_id,
        message_id,
        &initial_dashboard_text,
        keyboard.clone(),
    ).await?;

    let background_data = Box::new((
        bot_service.clone(),
        wallet_address,
        user_data.display_name().to_string(),
        config_text,
        chat_id,
        message_id,
        keyboard,
    ));

    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_address,
            ref display_name,
            ref config_text,
            chat_id,
            message_id,
            ref keyboard
        ) = *background_data;

        const DASHBOARD_HEADER: &str = "📊 <b>BSC Dashboard</b>\n\n";
        const BLOCKCHAIN_INFO: &str = "🔗 <b>Blockchain:</b> BSC (Binance Smart Chain)\n\n";
        const NETWORK_STATUS: &str = "📈 <b>Network Status:</b> Active\n\n";

        let blockchain_service = bot_service_clone.blockchain_service();
        let _token_info_service = bot_service_clone.token_info_service();

        // Get BNB price instantly from global cache (no async)
        let _bnb_price_usd = crate::service::token_info_service::TokenInfoService::get_price_sync("bnb");

        // Get balance with timeout to prevent hanging and ensure buttons remain clickable
        let balance = match tokio::time::timeout(
            std::time::Duration::from_secs(10),
            blockchain_service.get_balance_by_address(&wallet_address, Blockchain::BSC)
        ).await {
            Ok(Ok(balance)) => balance,
            Ok(Err(_)) => 0.0,
            Err(_) => 0.0, // Timeout - don't block the UI
        };

        let dashboard_text = {
            let mut text = String::with_capacity(512);
            use std::fmt::Write;

            text.push_str(DASHBOARD_HEADER);
            let _ = write!(text, "👤 <b>User:</b> {}\n\n", display_name);
            let _ = write!(text, "📍 <b>Address:</b> <code>{}</code>\n", wallet_address);

            let explorer_url = crate::constants::explorer_urls::get_explorer_address_url("bsc", &wallet_address);
            let _ = write!(text, "🔍 <a href=\"{}\">View on BSCScan</a>\n\n", explorer_url);

            text.push_str(BLOCKCHAIN_INFO);
            let _ = write!(text, "💰 <b>Balance:</b> <code>{:.6} BNB</code>\n\n", balance);
            text.push_str(NETWORK_STATUS);
            let _ = write!(text, "⚙️ <b>Settings:</b> {}", config_text);

            text
        };

        let _ = bot_service_clone.edit_message_with_keyboard(
            chat_id,
            message_id,
            &dashboard_text,
            keyboard.clone(),
        ).await;

        drop(dashboard_text);
    });

    Ok(())
}

pub fn format_config(user_data: &UserData, blockchain: Blockchain) -> String {
    let config = match user_data.get_config(&blockchain) {
        Some(config) => config,
        None => return "Not configured".to_string(),
    };

    let mut result = String::with_capacity(128);

    result.push_str("AntiRug: ");
    result.push_str(if config.antirug { "✅" } else { "❌" });
    result.push_str(", AntiMEV: ");
    result.push_str(if config.anti_mev { "✅" } else { "❌" });
    result.push_str(", AutoBuy: ");
    result.push_str(if config.auto_buy { "✅" } else { "❌" });
    result.push_str(", AutoSell: ");
    result.push_str(if config.auto_sell { "✅" } else { "❌" });
    result.push_str(", AutoApprove: ");
    result.push_str(if config.auto_approve { "✅" } else { "❌" });

    result
}

pub fn create_dashboard_keyboard(blockchain: Blockchain) -> InlineKeyboardMarkup {
    const BSC_VIEW_CMD: &str = "{\"command\":\"view_bsc\"}";
    const SOL_VIEW_CMD: &str = "{\"command\":\"view_sol\"}";
    const ETH_VIEW_CMD: &str = "{\"command\":\"view_eth\"}";
    const BASE_VIEW_CMD: &str = "{\"command\":\"view_base\"}";
    const CLEANUP_CMD: &str = "{\"command\":\"cleanup_chat\"}";
    const DISMISS_CMD: &str = "{\"command\":\"dismiss_message\"}";
    const HOME_CMD: &str = "{\"command\":\"home\"}";

    let mut keyboard = Vec::with_capacity(5);

    let scan_json = format!("{{\"command\":\"scan_contract_{}\"}}", blockchain);
    let refresh_json = format!("{{\"command\":\"refresh_wallet_{}\"}}", blockchain);

    keyboard.push(vec![
        InlineKeyboardButton::callback("🔍 Scan Contract", scan_json),
        InlineKeyboardButton::callback("🔄 Refresh Wallet", refresh_json)
    ]);

    let config_json = format!("{{\"command\":\"show_config_{}\"}}", blockchain);

    keyboard.push(vec![
        InlineKeyboardButton::callback("⚙️ Config", config_json)
    ]);

    let generate_json = format!("{{\"command\":\"generate_new_{}_wallet\"}}", blockchain);

    keyboard.push(vec![
        InlineKeyboardButton::callback("🔑 Generate New Wallet", generate_json)
    ]);

    let mut blockchain_buttons = Vec::with_capacity(3);

    if blockchain != Blockchain::BSC {
        blockchain_buttons.push(InlineKeyboardButton::callback("🔄 BSC", BSC_VIEW_CMD));
    }

    if blockchain != Blockchain::SOL {
        blockchain_buttons.push(InlineKeyboardButton::callback("🔄 SOL", SOL_VIEW_CMD));
    }

    if blockchain != Blockchain::ETH {
        blockchain_buttons.push(InlineKeyboardButton::callback("🔄 ETH", ETH_VIEW_CMD));
    }

    if blockchain != Blockchain::BASE {
        blockchain_buttons.push(InlineKeyboardButton::callback("🔄 BASE", BASE_VIEW_CMD));
    }

    keyboard.push(blockchain_buttons);

    keyboard.push(vec![
        InlineKeyboardButton::callback("🏠 Home", HOME_CMD),
        InlineKeyboardButton::callback("🧹 Clean chat", CLEANUP_CMD),
        InlineKeyboardButton::callback("❌ Dismiss", DISMISS_CMD)
    ]);

    InlineKeyboardMarkup::new(keyboard)
}

pub async fn show_buy_token_screen(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
) -> Result<(), BotError> {
    const LOADING_HEADER: &str = "🔄 <b>Preparing to buy token on BSC</b>\n\n";
    const LOADING_FOOTER: &str = "Getting price quote...";

    let chat_id = user_data.chat_id();

    let mut loading_text = String::with_capacity(256);
    loading_text.push_str(LOADING_HEADER);

    use std::fmt::Write;
    let _ = write!(loading_text, "Token: <code>{}</code>\n\n", token_address);
    loading_text.push_str(LOADING_FOOTER);

    let keyboard = InlineKeyboardMarkup::new(vec![vec![
        InlineKeyboardButton::callback("🔙 Back", "back"),
    ]]);

    bot_service.edit_message_with_keyboard(
        chat_id,
        message_id,
        &loading_text,
        keyboard,
    ).await?;

    let bot_service_clone = bot_service.clone();
    let token_address = token_address.to_string();

    tokio::spawn(async move {
        let token_service = TokenInfoService::new();
        let token_info_result = token_service.get_comprehensive_token_info(&token_address, &Blockchain::BSC).await;

        let token_name = match token_info_result {
            Ok(info) => {
                let mut name = String::with_capacity(info.name.len() + info.symbol.len() + 3);
                name.push_str(&info.name);
                name.push_str(" (");
                name.push_str(&info.symbol);
                name.push(')');
                name
            },
            Err(_) => {
                if token_address.len() > 10 {
                    let mut short = String::with_capacity(14);
                    short.push_str(&token_address[0..6]);
                    short.push_str("...");
                    short.push_str(&token_address[token_address.len()-4..]);
                    format!("Token {}", short)
                } else {
                    format!("Token {}", token_address)
                }
            }
        };

        let mut prompt_text = String::with_capacity(512);
        prompt_text.push_str("🛒 <b>Buy Token on BSC</b>\n\n");

        let _ = write!(prompt_text, "Token: <b>{}</b>\n", token_name);
        let _ = write!(prompt_text, "Address: <code>{}</code>\n\n", token_address);
        prompt_text.push_str("How much BNB do you want to spend?");

        if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id, message_id, &prompt_text).await {
            let user_states = crate::screens::scan_screen::get_user_states();

            let context = format!("buy_token:bsc:{}:{}", token_address, edited_msg.id.0);
            let mut states = user_states.write().await;
            states.insert(chat_id, context);
        }
    });

    Ok(())
}

pub async fn execute_buy_token(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
    amount: f64,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();

    let mut loading_text = String::with_capacity(256);
    loading_text.push_str("⏳ <b>Processing Buy Order...</b>\n\n");
    loading_text.push_str(&format!("Token: <code>{}</code>\n", token_address));
    loading_text.push_str(&format!("Amount: <b>{} BNB</b>\n\n", amount));
    loading_text.push_str("Please wait while your transaction is being processed...");

    bot_service.edit_message(
        chat_id,
        message_id,
        &loading_text,
    ).await?;

    let bot_clone = bot_service.bot().clone();
    let chat_id_clone = chat_id;
    let message_id_clone = message_id;
    let token_address_clone = token_address.to_string();
    let amount_clone = amount;



    let wallet = user_data.get_wallet(&Blockchain::BSC);

    let amount_str = {
        let bnb_decimals = 18;
        let decimal_multiplier = 10_u128.pow(bnb_decimals);
        let amount_in_wei = (amount * decimal_multiplier as f64) as u128;
        amount_in_wei.to_string()
    };

    let background_data = Box::new((
        bot_service.clone(),
        wallet.clone(),
        token_address.to_string(),
        amount,
        amount_str,
        chat_id,
        message_id,
    ));

    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_clone,
            ref token_address_clone,
            amount,
            ref amount_str,
            chat_id,
            message_id_clone,
        ) = *background_data;

        let token_decimals = {
            let result = bot_service_clone.blockchain_service()
                .get_token_decimals(&token_address_clone, &Blockchain::BSC)
                .await;

            result.unwrap_or(18)
        };

        let quote_result = {
            bot_service_clone.evm_trader_service()
                .get_swap_quote(
                    "BNB",
                    &token_address_clone,
                    &amount_str,
                    &wallet_clone.address,
                    &Blockchain::BSC
                )
                .await
        };

        match quote_result {
            Ok(quote) => {

                let quote_clone = quote.clone();
                let tx_result = {
                    // 🚀 AUTO-TRIGGER: Use high-performance EVM trader with fallback
                    bot_service_clone.execute_high_performance_evm_buy(&wallet_clone, quote_clone, &Blockchain::BSC, Some(chat_id))
                        .await
                };

                match tx_result {
                    Ok(tx_hash) => {
                        // Calculate tokens received from quote
                        let tokens_received = format_token_amount_with_decimals(&quote.buyAmount, token_decimals);

                        let success_text = format!(
                            "✅ <b>Buy Order Successful!</b>\n\n\
                            Amount Spent: <b>{} BNB</b>\n\
                            Tokens Received: <b>{}</b>\n\
                            Contract: <code>{}</code>\n\n\
                            🚀 <b>Token pre-approved for instant sells!</b>\n\
                            Your next sell will be faster with no approval needed.\n\n\
                            <a href=\"{}\">View Transaction on BSCScan</a>",
                            amount,
                            tokens_received,
                            token_address_clone,
                            crate::constants::explorer_urls::get_explorer_tx_url("bsc", &tx_hash)
                        );

                        // Store token address in user state for shorter callback data
                        {
                            let mut user_states = crate::screens::scan_screen::get_user_states().write().await;
                            user_states.insert(chat_id, format!("last_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone));
                        }

                        let success_keyboard = InlineKeyboardMarkup::new(vec![
                            vec![
                                InlineKeyboardButton::callback(
                                    "◀️ Back to Token",
                                    "back_to_last_token"
                                ),
                            ],
                            vec![
                                InlineKeyboardButton::callback("🏠 Dashboard", "{\"command\":\"new_msg_view_bsc\"}"),
                                InlineKeyboardButton::callback("❌ Dismiss", "dismiss_message"),
                            ]
                        ]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id,
                            message_id_clone,
                            &success_text,
                            success_keyboard,
                        ).await;
                    },
                    Err(e) => {
                        {
                            let mut error_text = String::with_capacity(256);
                            error_text.push_str("❌ <b>Error executing swap</b>\n\n");
                            error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                            error_text.push_str(&format!("Amount: {} BNB\n\n", amount));
                            error_text.push_str("The transaction could not be completed. Please try again later.");

                            println!("Swap execution error: {}", e);

                            let error_keyboard = InlineKeyboardMarkup::new(vec![
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🔄 Reload Token",
                                        format!("refresh_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone),
                                    )
                                ],
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🏠 BSC Dashboard",
                                        "{\"command\":\"view_bsc\"}",
                                    ),
                                    InlineKeyboardButton::callback(
                                        "❌ Dismiss",
                                        "dismiss_message",
                                    )
                                ]
                            ]);

                            let _ = bot_service_clone.edit_message_with_keyboard(
                                chat_id,
                                message_id_clone,
                                &error_text,
                                error_keyboard,
                            ).await;
                        }
                    }
                }
            },
            Err(e) => {
                {
                    let mut error_text = String::with_capacity(256);
                    error_text.push_str("❌ <b>Error getting swap quote</b>\n\n");
                    error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                    error_text.push_str(&format!("Amount: {} BNB\n\n", amount));
                    error_text.push_str("Unable to get a quote for this token. It may have low liquidity or trading may be restricted.");

                    println!("Swap quote error: {}", e);

                    let error_keyboard = InlineKeyboardMarkup::new(vec![
                        vec![
                            InlineKeyboardButton::callback(
                                "🔄 Reload Token",
                                format!("refresh_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone),
                            )
                        ],
                        vec![
                            InlineKeyboardButton::callback(
                                "🏠 BSC Dashboard",
                                "{\"command\":\"view_bsc\"}",
                            ),
                            InlineKeyboardButton::callback(
                                "❌ Dismiss",
                                "dismiss_message",
                            )
                        ]
                    ]);

                    let _ = bot_service_clone.edit_message_with_keyboard(
                        chat_id,
                        message_id_clone,
                        &error_text,
                        error_keyboard,
                    ).await;
                }
            }
        }


    });

    Ok(())
}

pub async fn show_sell_token_screen(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
) -> Result<(), BotError> {
    let (chat_id, _keyboard) = {
        let mut loading_text = String::with_capacity(256);
        loading_text.push_str("🔄 <b>Preparing to sell token on BSC</b>\n\n");
        loading_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address));
        loading_text.push_str("Getting token balance...");

        let kb = InlineKeyboardMarkup::new(vec![vec![
            InlineKeyboardButton::callback("🔙 Back", "back"),
        ]]);

        let chat = user_data.chat_id();
        bot_service.edit_message_with_keyboard(
            chat,
            message_id,
            &loading_text,
            kb.clone(),
        ).await?;

        (chat, kb)
    };

    let wallet = user_data.get_wallet(&Blockchain::BSC);

    let background_data = Box::new((
        bot_service.clone(),
        wallet.clone(),
        token_address.to_string(),
        chat_id,
        message_id,
    ));

    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_clone,
            ref token_address_clone,
            chat_id,
            message_id_clone
        ) = *background_data;

        let balance_result = {
            bot_service_clone.blockchain_service()
                .get_token_balance(&wallet_clone.address, &token_address_clone, &Blockchain::BSC)
                .await
        };

        match balance_result {
            Ok((balance, _decimals)) => {
                if balance <= 0.0 {
                    {
                        let mut error_text = String::with_capacity(256);
                        error_text.push_str("❌ <b>No tokens to sell</b>\n\n");
                        error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                        error_text.push_str("You don't have any tokens to sell.");

                        let keyboard = InlineKeyboardMarkup::new(vec![vec![
                            InlineKeyboardButton::callback("🔙 Back", "back"),
                        ]]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id,
                            message_id_clone,
                            &error_text,
                            keyboard,
                        ).await;
                    }
                    return;
                }

                let token_name = {
                    if token_address_clone.len() > 10 {
                        let mut short = String::with_capacity(14);
                        short.push_str(&token_address_clone[0..6]);
                        short.push_str("...");
                        short.push_str(&token_address_clone[token_address_clone.len()-4..]);
                        format!("Token {}", short)
                    } else {
                        format!("Token {}", token_address_clone)
                    }
                };

                let prompt_text = {
                    let mut text = String::with_capacity(512);
                    text.push_str("💰 <b>Sell Token on BSC</b>\n\n");
                    text.push_str(&format!("Token: <b>{}</b>\n", token_name));
                    text.push_str(&format!("Address: <code>{}</code>\n\n", token_address_clone));
                    text.push_str(&format!("Your Balance: {} tokens\n\n", balance));
                    text.push_str("Enter the percentage of tokens you want to sell (1-100):");
                    text
                };

                if let Ok(edited_msg) = bot_service_clone.edit_message(chat_id, message_id_clone, &prompt_text).await {
                    let user_states = crate::screens::scan_screen::get_user_states();

                    let context = format!("sell_token_custom:bsc:{}:{}", token_address_clone, edited_msg.id.0);
                    let mut states = user_states.write().await;
                    states.insert(chat_id, context);
                }
            },
            Err(e) => {
                {
                    let mut error_text = String::with_capacity(256);
                    error_text.push_str("❌ <b>Error getting token balance</b>\n\n");
                    error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                    error_text.push_str("Unable to retrieve token balance. Please try again later.");

                    println!("Token balance error: {}", e);

                    let keyboard = InlineKeyboardMarkup::new(vec![vec![
                        InlineKeyboardButton::callback("🔙 Back", "back"),
                    ]]);

                    let _ = bot_service_clone.edit_message_with_keyboard(
                        chat_id,
                        message_id_clone,
                        &error_text,
                        keyboard,
                    ).await;
                }
            }
        }
    });

    Ok(())
}

pub async fn execute_sell_token(
    bot_service: &BotService,
    user_data: &UserData,
    message_id: i32,
    token_address: &str,
    percentage: f64,
) -> Result<(), BotError> {
    let chat_id = user_data.chat_id();

    let mut loading_text = String::with_capacity(256);
    loading_text.push_str("⏳ <b>Processing Sell Order...</b>\n\n");
    loading_text.push_str(&format!("Token: <code>{}</code>\n", token_address));
    loading_text.push_str(&format!("Amount: <b>{:.1}%</b>\n\n", percentage * 100.0));
    loading_text.push_str("Please wait while your transaction is being processed...");

    bot_service.edit_message(
        chat_id,
        message_id,
        &loading_text,
    ).await?;

    let bot_clone = bot_service.bot().clone();
    let chat_id_clone = chat_id;
    let message_id_clone = message_id;
    let token_address_clone = token_address.to_string();
    let percentage_clone = percentage;



    let wallet = user_data.get_wallet(&Blockchain::BSC);

    let background_data = Box::new((
        bot_service.clone(),
        wallet.clone(),
        token_address.to_string(),
        percentage,
        chat_id,
        message_id,
    ));

    tokio::spawn(async move {
        let (
            ref bot_service_clone,
            ref wallet_clone,
            ref token_address_clone,
            percentage,
            chat_id,
            message_id_clone,
        ) = *background_data;

        let balance_result = {
            bot_service_clone.blockchain_service()
                .get_token_balance(&wallet_clone.address, &token_address_clone, &Blockchain::BSC)
                .await
        };

        match balance_result {
            Ok((balance, decimals)) => {
                // Apply a small buffer (0.99) when selling 100% to account for potential balance changes
                let adjusted_percentage = if (percentage * 100.0) as u64 == 100 {
                    0.99 // Use 99% instead of 100% to avoid "insufficient balance" errors
                } else {
                    percentage
                };

                let amount_to_sell = balance * adjusted_percentage;

                if amount_to_sell <= 0.0 {
                    {
                        let mut error_text = String::with_capacity(256);
                        error_text.push_str("❌ <b>No tokens to sell</b>\n\n");
                        error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                        error_text.push_str("You don't have any tokens to sell.");

                        let error_keyboard = InlineKeyboardMarkup::new(vec![
                            vec![
                                InlineKeyboardButton::callback(
                                    "🔄 Reload Token",
                                    format!("refresh_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone),
                                )
                            ],
                            vec![
                                InlineKeyboardButton::callback(
                                    "🏠 BSC Dashboard",
                                    "{\"command\":\"view_bsc\"}",
                                ),
                                InlineKeyboardButton::callback(
                                    "❌ Dismiss",
                                    "dismiss_message",
                                )
                            ]
                        ]);

                        let _ = bot_service_clone.edit_message_with_keyboard(
                            chat_id,
                            message_id_clone,
                            &error_text,
                            error_keyboard,
                        ).await;
                    }
                    return;
                }

                let amount_str = {
                    let decimal_multiplier = 10_u128.pow(decimals as u32);
                    let amount_in_smallest = (amount_to_sell * decimal_multiplier as f64) as u128;
                    amount_in_smallest.to_string()
                };
                let quote_result = {
                    bot_service_clone.evm_trader_service()
                        .get_swap_quote(
                            &token_address_clone,
                            "BNB",
                            &amount_str,
                            &wallet_clone.address,
                            &Blockchain::BSC
                        )
                        .await
                };

                match quote_result {
                    Ok(quote) => {

                        let quote_clone = quote.clone();
                        let tx_result = {
                            // 🚀 AUTO-TRIGGER: Use high-performance EVM trader with fallback for sell
                            bot_service_clone.execute_high_performance_evm_sell(&wallet_clone, quote_clone, &Blockchain::BSC, Some(chat_id))
                                .await
                        };

                        match tx_result {
                            Ok(tx_hash) => {
                                // Calculate BNB received from quote
                                let bnb_received = format_token_amount_with_decimals(&quote.buyAmount, 18);

                                let success_text = format!(
                                    "✅ <b>Sell Order Successful!</b>\n\n\
                                    Amount Sold: <b>{:.1}%</b>\n\
                                    BNB Received: <b>{} BNB</b>\n\
                                    Contract: <code>{}</code>\n\n\
                                    <a href=\"{}\">View Transaction on BSCScan</a>",
                                    percentage * 100.0,
                                    bnb_received,
                                    token_address_clone,
                                    crate::constants::explorer_urls::get_explorer_tx_url("bsc", &tx_hash)
                                );

                                // Store token address in user state for shorter callback data
                                {
                                    let mut user_states = crate::screens::scan_screen::get_user_states().write().await;
                                    user_states.insert(chat_id, format!("last_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone));
                                }

                                let success_keyboard = InlineKeyboardMarkup::new(vec![
                                    vec![
                                        InlineKeyboardButton::callback(
                                            "◀️ Back to Token",
                                            "back_to_last_token"
                                        ),
                                    ],
                                    vec![
                                        InlineKeyboardButton::callback("🏠 Dashboard", "{\"command\":\"new_msg_view_bsc\"}"),
                                        InlineKeyboardButton::callback("❌ Dismiss", "dismiss_message"),
                                    ]
                                ]);

                                let _ = bot_service_clone.edit_message_with_keyboard(
                                    chat_id,
                                    message_id_clone,
                                    &success_text,
                                    success_keyboard,
                                ).await;
                            },
                            Err(e) => {
                                {
                                    let mut error_text = String::with_capacity(256);
                                    error_text.push_str("❌ <b>Error executing swap</b>\n\n");
                                    error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                                    error_text.push_str(&format!("Amount: {} tokens ({}%)\n\n",
                                                               amount_to_sell,
                                                               (percentage * 100.0) as u64));
                                    error_text.push_str("The transaction could not be completed. Please try again later.");

                                    println!("Swap execution error: {}", e);

                                    let error_keyboard = InlineKeyboardMarkup::new(vec![
                                        vec![
                                            InlineKeyboardButton::callback(
                                                "🔄 Reload Token",
                                                format!("refresh_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone),
                                            )
                                        ],
                                        vec![
                                            InlineKeyboardButton::callback(
                                                "🏠 BSC Dashboard",
                                                "{\"command\":\"view_bsc\"}",
                                            ),
                                            InlineKeyboardButton::callback(
                                                "❌ Dismiss",
                                                "dismiss_message",
                                            )
                                        ]
                                    ]);

                                    let _ = bot_service_clone.edit_message_with_keyboard(
                                        chat_id,
                                        message_id_clone,
                                        &error_text,
                                        error_keyboard,
                                    ).await;
                                }
                            }
                        }
                    },
                    Err(e) => {
                        {
                            let mut error_text = String::with_capacity(256);
                            error_text.push_str("❌ <b>Error getting swap quote</b>\n\n");
                            error_text.push_str(&format!("Token: <code>{}</code>\n", token_address_clone));
                            error_text.push_str(&format!("Amount: {} tokens ({}%)\n\n",
                                                       amount_to_sell,
                                                       (percentage * 100.0) as u64));
                            error_text.push_str("Unable to get a quote for this token. It may have low liquidity or trading may be restricted.");

                            println!("Swap quote error: {}", e);

                            let error_keyboard = InlineKeyboardMarkup::new(vec![
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🔄 Reload Token",
                                        format!("refresh_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone),
                                    )
                                ],
                                vec![
                                    InlineKeyboardButton::callback(
                                        "🏠 BSC Dashboard",
                                        "{\"command\":\"view_bsc\"}",
                                    ),
                                    InlineKeyboardButton::callback(
                                        "❌ Dismiss",
                                        "dismiss_message",
                                    )
                                ]
                            ]);

                            let _ = bot_service_clone.edit_message_with_keyboard(
                                chat_id,
                                message_id_clone,
                                &error_text,
                                error_keyboard,
                            ).await;
                        }
                    }
                }
            },
            Err(e) => {
                {
                    let mut error_text = String::with_capacity(256);
                    error_text.push_str("❌ <b>Error getting token balance</b>\n\n");
                    error_text.push_str(&format!("Token: <code>{}</code>\n\n", token_address_clone));
                    error_text.push_str("Unable to retrieve token balance. Please try again later.");

                    println!("Token balance error: {}", e);

                    let error_keyboard = InlineKeyboardMarkup::new(vec![
                        vec![
                            InlineKeyboardButton::callback(
                                "🔄 Reload Token",
                                format!("refresh_token:{}:{}", Blockchain::BSC.as_str(), token_address_clone),
                            )
                        ],
                        vec![
                            InlineKeyboardButton::callback(
                                "🏠 BSC Dashboard",
                                "{\"command\":\"view_bsc\"}",
                            ),
                            InlineKeyboardButton::callback(
                                "❌ Dismiss",
                                "dismiss_message",
                            )
                        ]
                    ]);

                    let _ = bot_service_clone.edit_message_with_keyboard(
                        chat_id,
                        message_id_clone,
                        &error_text,
                        error_keyboard,
                    ).await;
                }
            }
        }


    });

    Ok(())
}

fn format_token_amount(amount_str: &str) -> String {
    let amount = match amount_str.parse::<f64>() {
        Ok(num) => num,
        Err(_) => return amount_str.to_string(),
    };

    let mut result = String::with_capacity(16);

    use std::fmt::Write;
    if amount >= 1e18 {
        let _ = write!(result, "{:.6}", amount / 1e18);
    } else if amount >= 1e9 {
        let _ = write!(result, "{:.6}", amount / 1e9);
    } else if amount >= 1e6 {
        let _ = write!(result, "{:.6}", amount / 1e6);
    } else {
        let _ = write!(result, "{:.6}", amount);
    }

    result
}

pub fn format_token_amount_with_decimals(amount_str: &str, decimals: u8) -> String {
    let amount = match amount_str.parse::<f64>() {
        Ok(num) => num,
        Err(_) => return amount_str.to_string(),
    };

    let divisor = 10_f64.powi(decimals as i32);

    let mut result = String::with_capacity(16);

    use std::fmt::Write;
    let _ = write!(result, "{:.6}", amount / divisor);

    result
}

