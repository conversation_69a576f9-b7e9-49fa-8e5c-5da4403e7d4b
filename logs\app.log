[2025-06-12T15:29:05Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-12T15:29:05Z INFO  Easybot] Starting EasyBot...
[2025-06-12T15:29:05Z INFO  Easybot] Environment variables for port configuration:
[2025-06-12T15:29:05Z INFO  Easybot]   PORT=8000
[2025-06-12T15:29:05Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-12T15:29:05Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-12T15:29:05Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-12T15:29:05Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-12T15:29:05Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-12T15:29:06Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-12T15:29:06Z INFO  Easybot] Initializing database connection...
[2025-06-12T15:29:25Z INFO  Easybot] Database connection established
[2025-06-12T15:29:25Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-12T15:29:26Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-12T15:29:27Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-12T15:29:27Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-12T15:29:27Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-12T15:29:27Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-12T15:29:27Z INFO  Easybot] Initializing cached price service...
[2025-06-12T15:29:27Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-12T15:29:27Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T15:29:28Z INFO  Easybot::service::price_service] DexScreener price for SOL: $157.96000000
[2025-06-12T15:29:28Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $157.9600
[2025-06-12T15:29:28Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $157.9600
[2025-06-12T15:29:29Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:29:30Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2745.53000000
[2025-06-12T15:29:30Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2745.5300
[2025-06-12T15:29:30Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2745.5300
[2025-06-12T15:29:31Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-06-12T15:29:32Z INFO  Easybot::service::price_service] DexScreener price for BSC: $661.66000000
[2025-06-12T15:29:32Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $661.6600
[2025-06-12T15:29:32Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $661.6600
[2025-06-12T15:29:33Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-06-12T15:29:34Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2747.71000000
[2025-06-12T15:29:34Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2747.7100
[2025-06-12T15:29:34Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2747.7100
[2025-06-12T15:29:34Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-12T15:29:34Z INFO  Easybot] Price cache initialized successfully
[2025-06-12T15:29:34Z INFO  Easybot] Starting background price updater...
[2025-06-12T15:29:34Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 1800s)
[2025-06-12T15:29:34Z INFO  Easybot] Background price updater started
[2025-06-12T15:29:34Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-12T15:29:34Z INFO  Easybot] Initializing Solana trader service...
[2025-06-12T15:29:35Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T15:29:35Z INFO  Easybot::service::price_service] Using cached native price for SOL: $157.9600
[2025-06-12T15:29:35Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $157.9600
[2025-06-12T15:29:35Z INFO  Easybot] Solana trader service initialized
[2025-06-12T15:29:35Z INFO  Easybot] Initializing snipe worker...
[2025-06-12T15:29:35Z INFO  Easybot] Snipe worker initialized and started
[2025-06-12T15:29:35Z INFO  Easybot] Starting background fee collection service...
[2025-06-12T15:29:35Z INFO  Easybot] Background fee collection service started
[2025-06-12T15:29:35Z INFO  Easybot] Starting bot dispatcher...
[2025-06-12T15:29:40Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:29:40Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2745.5300
[2025-06-12T15:29:40Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2745.5300
[2025-06-12T15:29:46Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-06-12T15:29:46Z INFO  Easybot::service::price_service] Using cached native price for BSC: $661.6600
[2025-06-12T15:29:46Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $661.6600
[2025-06-12T15:29:51Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-06-12T15:29:51Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2747.7100
[2025-06-12T15:29:51Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2747.7100
[2025-06-12T15:29:52Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-12T15:29:57Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-12T15:30:01Z INFO  Easybot] Received callback query with data: sell_token:base:0x0b3e328455c4059EEb9e3f84b5543F74E24e7E1b
[2025-06-12T15:30:51Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2748.61000000
[2025-06-12T15:30:51Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009096 tokens ($0.25 USD at $2748.6100/token)
[2025-06-12T15:30:51Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000054 below minimum 0.00009096 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:30:51Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2748.6100
[2025-06-12T15:30:51Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00010822, percentage=0.50%, calculated=0.00000054, final=0.00009096 ETH ($0.2500)
[2025-06-12T15:30:54Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2748.61000000
[2025-06-12T15:30:54Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009096 tokens ($0.25 USD at $2748.6100/token)
[2025-06-12T15:30:54Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000054 below minimum 0.00009096 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:30:54Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2748.6100
[2025-06-12T15:30:54Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00010822, percentage=0.50%, calculated=0.00000054, final=0.00009096 ETH ($0.2500)
[2025-06-12T15:30:54Z INFO  Easybot::service::admin_fee_service] 💰 Fee adjusted for minimum threshold: 0.00000054 → 0.00009096 ETH for BASE
[2025-06-12T15:30:54Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=684af2ae9f34d48926b315b5, user_id=683cab67f5800b0c46d3ba1b, blockchain=BASE, type=SellFee, fee_amount=0.00009095506455990483
[2025-06-12T15:30:56Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2748.61000000
[2025-06-12T15:30:56Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009096 tokens ($0.25 USD at $2748.6100/token)
[2025-06-12T15:30:56Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000054 below minimum 0.00009096 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:31:07Z INFO  Easybot::service::admin_fee_service] Marked fee transaction as completed: id=684af2ae9f34d48926b315b5, hash=0x3c89cc334f9d0388878faaf3e917ec02d4a8d500bddae4daedcd48404b4d175a
[2025-06-12T15:36:43Z INFO  Easybot::controllers::auth_controller] Login attempt for user: Dev
[2025-06-12T15:36:46Z INFO  Easybot::controllers::auth_controller] Successful login for user: Dev in 2.5621703s
[2025-06-12T15:36:46Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-12T15:36:46Z INFO  Easybot::controllers::analytics_controller] Calculating dashboard analytics for period: 1749656206 to 1749742606
[2025-06-12T15:36:48Z WARN  Easybot::service::cached_price_service] Database error getting cached price for ETH: Kind: unknown variant `ETH`, expected one of `bsc`, `sol`, `eth`, `base`, labels: {}
[2025-06-12T15:36:48Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:36:49Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2755.86000000
[2025-06-12T15:36:49Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2755.8600
[2025-06-12T15:36:49Z WARN  Easybot::service::cached_price_service] Database error getting cached price for SOL: Kind: unknown variant `SOL`, expected one of `bsc`, `sol`, `eth`, `base`, labels: {}
[2025-06-12T15:36:49Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T15:36:50Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.63000000
[2025-06-12T15:36:50Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $158.6300
[2025-06-12T15:36:50Z WARN  Easybot::service::cached_price_service] Database error getting cached price for ETH: Kind: unknown variant `ETH`, expected one of `bsc`, `sol`, `eth`, `base`, labels: {}
[2025-06-12T15:36:50Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:36:52Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2755.86000000
[2025-06-12T15:36:52Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2755.8600
[2025-06-12T15:36:52Z INFO  Easybot::controllers::analytics_controller] Admin fees aggregated - Total: $1.98, 24h: $0.50
[2025-06-12T15:36:52Z INFO  Easybot::controllers::analytics_controller] Dashboard data generated in 5.9105954s
[2025-06-12T15:36:56Z INFO  Easybot::controllers::users_controller] Users list requested by admin: Dev
[2025-06-12T15:36:59Z INFO  Easybot::controllers::users_controller] Users list generated for Dev in 3.5357738s
[2025-06-12T15:37:04Z INFO  Easybot::controllers::analytics_controller] Dashboard data requested by admin: 683a0b0944ad9dc5fa324858
[2025-06-12T15:37:50Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-06-12T15:37:50Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
[2025-06-12T15:37:56Z INFO  Easybot::controllers::transactions_controller] Transactions list requested by admin: Dev
[2025-06-12T15:37:56Z INFO  Easybot::controllers::transactions_controller] Transactions list generated for Dev in 623.682ms
[2025-06-12T15:37:56Z INFO  Easybot::controllers::transactions_controller] Fee statistics requested by admin: Dev
[2025-06-12T15:37:57Z WARN  Easybot::service::cached_price_service] Database error getting cached price for ETH: Kind: unknown variant `ETH`, expected one of `bsc`, `sol`, `eth`, `base`, labels: {}
[2025-06-12T15:37:57Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:37:59Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2753.15000000
[2025-06-12T15:37:59Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2753.1500
[2025-06-12T15:37:59Z WARN  Easybot::service::cached_price_service] Database error getting cached price for ETH: Kind: unknown variant `ETH`, expected one of `bsc`, `sol`, `eth`, `base`, labels: {}
[2025-06-12T15:37:59Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:38:01Z INFO  teloxide::utils::shutdown_token] Dispatching has been shut down.
[2025-06-12T15:38:01Z INFO  teloxide::dispatching::dispatcher] dispatcher is shutdown...
[2025-06-12T15:38:01Z INFO  Easybot] Bot stopped
