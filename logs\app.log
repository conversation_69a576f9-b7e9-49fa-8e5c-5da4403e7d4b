[2025-06-12T16:28:43Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-12T16:28:43Z INFO  Easybot] Starting EasyBot...
[2025-06-12T16:28:43Z INFO  Easybot] Environment variables for port configuration:
[2025-06-12T16:28:43Z INFO  Easybot]   PORT=8000
[2025-06-12T16:28:43Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-12T16:28:43Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-12T16:28:43Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-12T16:28:43Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-12T16:28:43Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-12T16:28:43Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-12T16:28:43Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-12T16:28:43Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-12T16:28:43Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-12T16:28:43Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-12T16:28:44Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-12T16:28:44Z INFO  Easybot] Initializing database connection...
[2025-06-12T16:29:07Z INFO  Easybot] Database connection established
[2025-06-12T16:29:07Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-12T16:29:09Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-12T16:29:10Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-12T16:29:10Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-12T16:29:10Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-12T16:29:10Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-12T16:29:10Z INFO  Easybot] Initializing cached price service...
[2025-06-12T16:29:10Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-12T16:29:10Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T16:29:16Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.35000000
[2025-06-12T16:29:16Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $158.3500
[2025-06-12T16:29:17Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $158.3500
[2025-06-12T16:29:17Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T16:29:18Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2741.19000000
[2025-06-12T16:29:18Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2741.1900
[2025-06-12T16:29:18Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2741.1900
[2025-06-12T16:29:19Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-06-12T16:29:22Z INFO  Easybot::service::price_service] DexScreener price for BSC: $663.97000000
[2025-06-12T16:29:22Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $663.9700
[2025-06-12T16:29:23Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $663.9700
[2025-06-12T16:29:23Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-06-12T16:29:24Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2741.38000000
[2025-06-12T16:29:24Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2741.3800
[2025-06-12T16:29:24Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2741.3800
[2025-06-12T16:29:24Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-12T16:29:24Z INFO  Easybot] Price cache initialized successfully
[2025-06-12T16:29:24Z INFO  Easybot] Starting background price updater...
[2025-06-12T16:29:24Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 1800s)
[2025-06-12T16:29:24Z INFO  Easybot] Background price updater started
[2025-06-12T16:29:24Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-12T16:29:24Z INFO  Easybot] Initializing Solana trader service...
[2025-06-12T16:29:25Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T16:29:25Z INFO  Easybot::service::price_service] Using cached native price for SOL: $158.3500
[2025-06-12T16:29:25Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $158.3500
[2025-06-12T16:29:26Z INFO  Easybot] Solana trader service initialized
[2025-06-12T16:29:26Z INFO  Easybot] Initializing snipe worker...
[2025-06-12T16:29:26Z INFO  Easybot] Snipe worker initialized and started
[2025-06-12T16:29:26Z INFO  Easybot] Starting background fee collection service...
[2025-06-12T16:29:26Z INFO  Easybot] Background fee collection service started
[2025-06-12T16:29:26Z INFO  Easybot] Starting bot dispatcher...
[2025-06-12T16:29:29Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-12T16:29:31Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T16:29:31Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2741.1900
[2025-06-12T16:29:31Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2741.1900
[2025-06-12T16:29:35Z ERROR teloxide::error_handlers] Error: TelegramError(Api(Unknown("Bad Request: query is too old and response timeout expired or query ID is invalid")))
[2025-06-12T16:29:35Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-12T16:29:37Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-06-12T16:29:37Z INFO  Easybot::service::price_service] Using cached native price for BSC: $663.9700
[2025-06-12T16:29:37Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $663.9700
[2025-06-12T16:29:41Z INFO  Easybot] Received callback query with data: spend_token:sol:DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2
[2025-06-12T16:29:42Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-06-12T16:29:42Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2741.3800
[2025-06-12T16:29:42Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2741.3800
[2025-06-12T16:29:47Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-12T16:29:58Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.56000000
[2025-06-12T16:29:58Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157669 tokens ($0.25 USD at $158.5600/token)
[2025-06-12T16:29:58Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00157669 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:30:00Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.56000000
[2025-06-12T16:30:00Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157669 tokens ($0.25 USD at $158.5600/token)
[2025-06-12T16:30:00Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00157669 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:30:00Z INFO  Easybot::service::price_service] Using cached native price for SOL: $158.5600
[2025-06-12T16:30:00Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00001000, percentage=0.50%, calculated=0.00000005, final=0.00157669 SOL ($0.2500)
[2025-06-12T16:30:00Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=684b00881c0a9fd5f346c232, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=BuyFee, fee_amount=0.0015766902119071645
[2025-06-12T16:30:01Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.56000000
[2025-06-12T16:30:01Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157669 tokens ($0.25 USD at $158.5600/token)
[2025-06-12T16:30:01Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00157669 below minimum 0.00157669 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:30:07Z INFO  Easybot::service::admin_fee_service] ✅ Fee transaction completed with adjusted amount: id=684b00881c0a9fd5f346c232, hash=4hdR3jCDX8WbJfmq6B8Vdi9ikJXzL9JYuv1QbkftySEVbyB3NGmjQStNqREbVGhNrm7oyBHhQ77gTVVtTtfxvQxV, actual_fee=0.00157669
[2025-06-12T16:30:10Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.56000000
[2025-06-12T16:30:10Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157669 tokens ($0.25 USD at $158.5600/token)
[2025-06-12T16:30:10Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00157669 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:30:10Z INFO  Easybot::service::price_service] Using cached native price for SOL: $158.5600
[2025-06-12T16:30:10Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00001000, percentage=0.50%, calculated=0.00000005, final=0.00157669 SOL ($0.2500)
[2025-06-12T16:31:43Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-12T16:31:50Z INFO  Easybot] Received callback query with data: sell_token:sol:DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2
[2025-06-12T16:32:04Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.40000000
[2025-06-12T16:32:04Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157828 tokens ($0.25 USD at $158.4000/token)
[2025-06-12T16:32:04Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000016 below minimum 0.00157828 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:32:04Z INFO  Easybot::service::price_service] Using cached native price for SOL: $158.4000
[2025-06-12T16:32:04Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00003106, percentage=0.50%, calculated=0.00000016, final=0.00157828 SOL ($0.2500)
[2025-06-12T16:32:08Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.40000000
[2025-06-12T16:32:08Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157828 tokens ($0.25 USD at $158.4000/token)
[2025-06-12T16:32:08Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000015 below minimum 0.00157828 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:32:10Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.40000000
[2025-06-12T16:32:10Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157828 tokens ($0.25 USD at $158.4000/token)
[2025-06-12T16:32:10Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000016 below minimum 0.00157828 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:32:10Z INFO  Easybot::service::price_service] Using cached native price for SOL: $158.4000
[2025-06-12T16:32:10Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00003106, percentage=0.50%, calculated=0.00000016, final=0.00157828 SOL ($0.2500)
[2025-06-12T16:32:11Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=684b010b1c0a9fd5f346c239, user_id=683cab67f5800b0c46d3ba1b, blockchain=SOL, type=SellFee, fee_amount=0.0015782828282828283
[2025-06-12T16:32:12Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.40000000
[2025-06-12T16:32:12Z INFO  Easybot::service::price_service] Minimum fee for SOL: 0.00157828 tokens ($0.25 USD at $158.4000/token)
[2025-06-12T16:32:12Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00157828 below minimum 0.00157828 for SOL, adjusting to minimum $0.25 equivalent
[2025-06-12T16:36:14Z INFO  teloxide::dispatching::dispatcher] ^C received, trying to shutdown the dispatcher...
[2025-06-12T16:36:14Z INFO  teloxide::utils::shutdown_token] Trying to shutdown the dispatcher...
[2025-06-12T16:36:20Z INFO  teloxide::utils::shutdown_token] Dispatching has been shut down.
[2025-06-12T16:36:20Z INFO  Easybot] Bot stopped
