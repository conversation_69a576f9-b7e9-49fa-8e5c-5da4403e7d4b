[2025-06-12T15:29:05Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-12T15:29:05Z INFO  Easybot] Starting EasyBot...
[2025-06-12T15:29:05Z INFO  Easybot] Environment variables for port configuration:
[2025-06-12T15:29:05Z INFO  Easybot]   PORT=8000
[2025-06-12T15:29:05Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-12T15:29:05Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-12T15:29:05Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-12T15:29:05Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-12T15:29:05Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-12T15:29:05Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-12T15:29:06Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-12T15:29:06Z INFO  Easybot] Initializing database connection...
