[2025-06-12T15:46:34Z INFO  Easybot::utils::logging] Logger initialized with level: INFO
[2025-06-12T15:46:34Z INFO  Easybot] Starting EasyBot...
[2025-06-12T15:46:34Z INFO  Easybot] Environment variables for port configuration:
[2025-06-12T15:46:34Z INFO  Easybot]   PORT=8000
[2025-06-12T15:46:34Z INFO  Easybot]   SOCKET_PORT=3000
[2025-06-12T15:46:34Z INFO  Easybot] Using port 8000 from config for health check service
[2025-06-12T15:46:34Z INFO  Easybot] Using port 3000 from config for socket service
[2025-06-12T15:46:34Z INFO  Easybot] Starting health check service on port 8000...
[2025-06-12T15:46:34Z INFO  Easybot] Initializing admin service and controllers...
[2025-06-12T15:46:34Z INFO  Easybot::service::health_service] Starting server on port 8000
[2025-06-12T15:46:34Z INFO  Easybot::service::health_service] Current working directory: "D:\\workspace\\.rust\\Easybot"
[2025-06-12T15:46:34Z INFO  Easybot::service::health_service] Trying to bind to 0.0.0.0:8000
[2025-06-12T15:46:34Z INFO  Easybot::service::health_service] Server successfully started on port 8000
[2025-06-12T15:46:34Z INFO  Easybot::service::health_service] Server is now running on port 8000
[2025-06-12T15:46:34Z INFO  Easybot] Health check and admin services initialization completed
[2025-06-12T15:46:34Z INFO  Easybot] Initializing database connection...
[2025-06-12T15:47:01Z INFO  Easybot] Database connection established
[2025-06-12T15:47:01Z INFO  Easybot] Checking and initializing default super admin...
[2025-06-12T15:47:01Z INFO  Easybot::service::admin_init_service] SuperAdmin already exists. Skipping default admin creation.
[2025-06-12T15:47:03Z WARN  Easybot::service::admin_init_service] ⚠️  SECURITY WARNING: Default admin is still using default password!
[2025-06-12T15:47:03Z WARN  Easybot::service::admin_init_service] ⚠️  Please change the password immediately for security!
[2025-06-12T15:47:03Z WARN  Easybot::service::admin_init_service] ⚠️  Username: Olajosh
[2025-06-12T15:47:03Z WARN  Easybot::service::admin_init_service] ⚠️  Email: <EMAIL>
[2025-06-12T15:47:03Z INFO  Easybot] Initializing cached price service...
[2025-06-12T15:47:03Z INFO  Easybot::service::cached_price_service] Initializing price cache for all supported blockchains...
[2025-06-12T15:47:03Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T15:47:04Z INFO  Easybot::service::price_service] DexScreener price for SOL: $158.32000000
[2025-06-12T15:47:04Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $158.3200
[2025-06-12T15:47:04Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for SOL: $158.3200
[2025-06-12T15:47:05Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:47:07Z INFO  Easybot::service::price_service] DexScreener price for ETH: $2745.33000000
[2025-06-12T15:47:07Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2745.3300
[2025-06-12T15:47:07Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for ETH: $2745.3300
[2025-06-12T15:47:07Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-06-12T15:47:08Z INFO  Easybot::service::price_service] DexScreener price for BSC: $662.12000000
[2025-06-12T15:47:08Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $662.1200
[2025-06-12T15:47:08Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BSC: $662.1200
[2025-06-12T15:47:09Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-06-12T15:47:10Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2746.00770000
[2025-06-12T15:47:10Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2746.0077
[2025-06-12T15:47:10Z INFO  Easybot::service::cached_price_service] ✅ Initialized cache for BASE: $2746.0077
[2025-06-12T15:47:10Z INFO  Easybot::service::cached_price_service] Price cache initialization completed
[2025-06-12T15:47:10Z INFO  Easybot] Price cache initialized successfully
[2025-06-12T15:47:10Z INFO  Easybot] Starting background price updater...
[2025-06-12T15:47:10Z INFO  Easybot::service::cached_price_service] Starting background price updater (interval: 1800s)
[2025-06-12T15:47:10Z INFO  Easybot] Background price updater started
[2025-06-12T15:47:10Z INFO  Easybot::service::cached_price_service] 🔄 Background price update started
[2025-06-12T15:47:10Z INFO  Easybot] Initializing Solana trader service...
[2025-06-12T15:47:11Z INFO  Easybot] Solana trader service initialized
[2025-06-12T15:47:11Z INFO  Easybot] Initializing snipe worker...
[2025-06-12T15:47:11Z INFO  Easybot] Snipe worker initialized and started
[2025-06-12T15:47:11Z INFO  Easybot] Starting background fee collection service...
[2025-06-12T15:47:11Z INFO  Easybot] Background fee collection service started
[2025-06-12T15:47:11Z INFO  Easybot] Starting bot dispatcher...
[2025-06-12T15:47:11Z INFO  Easybot::service::cached_price_service] Fetching fresh price for SOL with rate limiting...
[2025-06-12T15:47:11Z INFO  Easybot::service::price_service] Using cached native price for SOL: $158.3200
[2025-06-12T15:47:11Z INFO  Easybot::service::cached_price_service] Fetched fresh price for SOL: $158.3200
[2025-06-12T15:47:14Z INFO  Easybot] Received callback query with data: back_to_last_token
[2025-06-12T15:47:16Z INFO  Easybot::service::cached_price_service] Fetching fresh price for ETH with rate limiting...
[2025-06-12T15:47:16Z INFO  Easybot::service::price_service] Using cached native price for ETH: $2745.3300
[2025-06-12T15:47:16Z INFO  Easybot::service::cached_price_service] Fetched fresh price for ETH: $2745.3300
[2025-06-12T15:47:21Z INFO  Easybot] Received callback query with data: spend_token:base:******************************************
[2025-06-12T15:47:21Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BSC with rate limiting...
[2025-06-12T15:47:21Z INFO  Easybot::service::price_service] Using cached native price for BSC: $662.1200
[2025-06-12T15:47:21Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BSC: $662.1200
[2025-06-12T15:47:27Z INFO  Easybot::service::cached_price_service] Fetching fresh price for BASE with rate limiting...
[2025-06-12T15:47:27Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2746.0077
[2025-06-12T15:47:27Z INFO  Easybot::service::cached_price_service] Fetched fresh price for BASE: $2746.0077
[2025-06-12T15:47:32Z INFO  Easybot::service::cached_price_service] 🔄 Background price update completed
[2025-06-12T15:47:45Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2742.57000000
[2025-06-12T15:47:45Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009116 tokens ($0.25 USD at $2742.5700/token)
[2025-06-12T15:47:45Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00009116 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:47:46Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2742.57000000
[2025-06-12T15:47:46Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009116 tokens ($0.25 USD at $2742.5700/token)
[2025-06-12T15:47:46Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00009116 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:47:46Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2742.5700
[2025-06-12T15:47:46Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00001000, percentage=0.50%, calculated=0.00000005, final=0.00009116 ETH ($0.2500)
[2025-06-12T15:47:46Z INFO  Easybot::service::admin_fee_service] 💰 Fee adjusted for minimum threshold: 0.00000005 → 0.00009116 ETH for BASE
[2025-06-12T15:47:47Z INFO  Easybot::service::admin_fee_service] Created admin fee transaction: id=684af6a22d90cfc5826f4522, user_id=683cab67f5800b0c46d3ba1b, blockchain=BASE, type=BuyFee, fee_amount=0.00009115537616177526
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2742.57000000
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009116 tokens ($0.25 USD at $2742.5700/token)
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00009116 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] Using cached native price for BASE: $2742.5700
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] 🔥 Admin fee calculation: base=0.00001000, percentage=0.50%, calculated=0.00000005, final=0.00009116 ETH ($0.2500)
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] DexScreener price for BASE: $2742.57000000
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] Minimum fee for BASE: 0.00009116 tokens ($0.25 USD at $2742.5700/token)
[2025-06-12T15:47:47Z INFO  Easybot::service::price_service] 💰 Fee amount 0.00000005 below minimum 0.00009116 for BASE, adjusting to minimum $0.25 equivalent
[2025-06-12T15:47:57Z INFO  Easybot::service::admin_fee_service] ✅ Fee transaction completed with adjusted amount: id=684af6a22d90cfc5826f4522, hash=0xdcaed72d01de47a35a36f65108a6de3d4d6574e9cd10cb8994382e9d8d87478f, actual_fee=0.00000005
[2025-06-12T15:47:58Z WARN  Easybot::service::admin_fee_service] ⚠️ Fee transaction 684af6a22d90cfc5826f4522 has no linked trade_id
