use crate::model::{Blockchain, Trade, Wallet};
use crate::service::{
    price_service::{PriceService, FeeCalculationResult},
    db_service::DbService,
};
use crate::service::solana_trader_service::SolanaTraderService;
use crate::service::evm_trader_service::EvmTraderService;
use mongodb::bson::{doc, oid::ObjectId};
use solana_sdk::signature::Keypair;
use std::str::FromStr;
use tracing::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime};
use chrono;
use hex;

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminFeeCollectionResult {
    pub success: bool,
    pub transaction_hash: Option<String>,
    pub fee_amount: f64,
    pub fee_amount_formatted: String,
    pub fee_usd_value: f64,
    pub blockchain: String,
    pub native_symbol: String,
    pub collection_method: String,
    pub error_message: Option<String>,
    pub skipped_reason: Option<String>,
}

pub struct AdminFeeCollector {
    price_service: PriceService,
}

impl AdminFeeCollector {
    pub fn new() -> Self {
        Self {
            price_service: PriceService::new(),
        }
    }

    /// Main entry point for collecting admin fees
    pub async fn collect_admin_fee(&self, transaction_id: &str) -> Result<AdminFeeCollectionResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("🚀 Starting high-performance admin fee collection for transaction: {}", transaction_id);

        // Get transaction from database
        let trade = self.get_transaction(transaction_id).await?;
        
        // Check if fee collection is needed
        if let Some(status) = &trade.admin_fee_status {
            if status == "completed" {
                return Ok(AdminFeeCollectionResult {
                    success: true,
                    transaction_hash: trade.admin_fee_transaction_id.map(|id| id.to_hex()),
                    fee_amount: trade.admin_fee_amount.unwrap_or(0.0),
                    fee_amount_formatted: "Already collected".to_string(),
                    fee_usd_value: 0.0,
                    blockchain: format!("{:?}", trade.blockchain),
                    native_symbol: self.get_native_symbol(&trade.blockchain),
                    collection_method: "already_collected".to_string(),
                    error_message: None,
                    skipped_reason: Some("Fee already collected".to_string()),
                });
            }
        }

        // Calculate and validate fee
        let mut fee_calculation = self.calculate_fee_for_trade(&trade).await?;

        if !fee_calculation.meets_minimum {
            info!("💰 Adjusting fee to minimum $0.25 equivalent: {} → {} USD",
                  fee_calculation.fee_usd_value, 0.25);

            // Get adjusted fee amount (minimum $0.25 equivalent)
            let adjusted_fee_amount = match self.price_service.get_adjusted_fee_amount(&trade.blockchain, fee_calculation.fee_amount).await {
                Ok(adjusted_amount) => adjusted_amount,
                Err(e) => {
                    error!("Failed to get adjusted fee amount: {}", e);
                    return Err(e);
                }
            };

            // Update fee calculation with adjusted amount
            fee_calculation.fee_amount = adjusted_fee_amount;
            fee_calculation.fee_amount_formatted = self.price_service.format_fee_amount(adjusted_fee_amount, &trade.blockchain);
            fee_calculation.fee_usd_value = 0.25; // Minimum USD value
            fee_calculation.meets_minimum = true;

            info!("✅ Fee adjusted to minimum: {} {} (${:.2})",
                  fee_calculation.fee_amount_formatted, fee_calculation.native_symbol, fee_calculation.fee_usd_value);
        } else {
            info!("✅ Fee meets minimum threshold, using calculated amount: {} {} (${:.4})",
                  fee_calculation.fee_amount_formatted, fee_calculation.native_symbol, fee_calculation.fee_usd_value);
        }

        // Collect fee based on blockchain
        let result = match trade.blockchain {
            Blockchain::SOL => self.collect_solana_admin_fee(&trade, &fee_calculation).await,
            Blockchain::ETH | Blockchain::BSC | Blockchain::BASE => {
                self.collect_evm_admin_fee(&trade, &fee_calculation).await
            }
        };

        match result {
            Ok(mut collection_result) => {
                // Update database with success
                self.update_fee_status(&trade, "completed", collection_result.transaction_hash.as_deref()).await?;
                collection_result.success = true;
                info!("✅ Admin fee collection completed successfully for transaction: {}", transaction_id);
                Ok(collection_result)
            }
            Err(e) => {
                error!("❌ Admin fee collection failed for transaction {}: {}", transaction_id, e);
                
                // Update database with failure
                self.update_fee_status(&trade, "failed", None).await?;
                
                Ok(AdminFeeCollectionResult {
                    success: false,
                    transaction_hash: None,
                    fee_amount: fee_calculation.fee_amount,
                    fee_amount_formatted: fee_calculation.fee_amount_formatted,
                    fee_usd_value: fee_calculation.fee_usd_value,
                    blockchain: fee_calculation.blockchain,
                    native_symbol: fee_calculation.native_symbol,
                    collection_method: "failed".to_string(),
                    error_message: Some(e.to_string()),
                    skipped_reason: None,
                })
            }
        }
    }

    /// Get transaction from database
    async fn get_transaction(&self, transaction_id: &str) -> Result<Trade, Box<dyn std::error::Error + Send + Sync>> {
        let object_id = ObjectId::parse_str(transaction_id)?;
        let db = DbService::get_db();
        let collection = db.collection::<Trade>("trades");
        
        let trade = collection
            .find_one(doc! { "_id": object_id }, None)
            .await?
            .ok_or("Transaction not found")?;
            
        Ok(trade)
    }

    /// Calculate fee for a trade
    async fn calculate_fee_for_trade(&self, trade: &Trade) -> Result<FeeCalculationResult, Box<dyn std::error::Error + Send + Sync>> {
        // Use existing admin fee if available, otherwise calculate
        if let Some(existing_fee) = trade.admin_fee_amount {
            let fee_percentage = trade.admin_fee_percentage.unwrap_or(2.5);
            return self.price_service.calculate_admin_fee(&trade.blockchain, existing_fee, 100.0).await;
        }

        // Calculate based on transaction type and amount
        let base_amount = if trade.trade_type == "buy" {
            trade.native_token_amount.or(trade.amount_out).unwrap_or(0.0)
        } else {
            trade.native_token_amount.unwrap_or(0.0)
        };

        let fee_percentage = trade.admin_fee_percentage.unwrap_or(2.5);
        self.price_service.calculate_admin_fee(&trade.blockchain, base_amount, fee_percentage).await
    }

    /// Update fee status in database
    async fn update_fee_status(
        &self,
        trade: &Trade,
        status: &str,
        transaction_hash: Option<&str>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<Trade>("trades");

        let mut update_doc = doc! {
            "$set": {
                "admin_fee_status": status,
                "updated_at": mongodb::bson::DateTime::from_chrono(chrono::Utc::now())
            }
        };

        if let Some(hash) = transaction_hash {
            // Store the transaction hash as a string, not ObjectId
            update_doc.get_document_mut("$set").unwrap().insert("admin_fee_transaction_hash", hash);
        }

        let result = collection
            .update_one(doc! { "_id": trade.id }, update_doc, None)
            .await?;

        if result.modified_count > 0 {
            info!("✅ Successfully updated admin fee status to '{}' for trade: {:?}", status, trade.id);
        } else {
            warn!("⚠️ No documents were modified when updating admin fee status for trade: {:?}", trade.id);
        }

        // Also create/update admin fee transaction record for better tracking
        if status == "completed" {
            self.create_admin_fee_transaction_record(trade, transaction_hash).await?;
        }

        Ok(())
    }

    /// Create admin fee transaction record for completed fees
    async fn create_admin_fee_transaction_record(
        &self,
        trade: &Trade,
        transaction_hash: Option<&str>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::AdminFeeTransaction>("admin_fee_transactions");

        let fee_amount = trade.admin_fee_amount.unwrap_or(0.0);
        let fee_percentage = trade.admin_fee_percentage.unwrap_or(2.5);
        let native_symbol = self.get_native_symbol(&trade.blockchain);

        let admin_fee_transaction = crate::model::AdminFeeTransaction {
            id: None,
            user_id: trade.user_id,
            trade_id: trade.id,
            blockchain: trade.blockchain.clone(),
            transaction_type: if trade.trade_type == "buy" {
                crate::model::admin_fee_transaction::FeeTransactionType::BuyFee
            } else {
                crate::model::admin_fee_transaction::FeeTransactionType::SellFee
            },
            status: crate::model::admin_fee_transaction::FeeTransactionStatus::Completed,
            fee_percentage,
            original_amount: trade.amount_out.unwrap_or(0.0),
            fee_amount,
            fee_token_symbol: native_symbol.clone(),
            fee_token_address: match trade.blockchain {
                crate::model::blockchain::Blockchain::SOL => "So11111111111111111111111111111111111111112".to_string(),
                crate::model::blockchain::Blockchain::ETH => "******************************************".to_string(),
                crate::model::blockchain::Blockchain::BSC => "******************************************".to_string(),
                crate::model::blockchain::Blockchain::BASE => "******************************************".to_string(),
            },
            transaction_hash: transaction_hash.map(|h| h.to_string()),
            admin_address: "".to_string(), // Will be filled by the calling function
            user_wallet_address: "".to_string(), // Trade model doesn't have wallet_address field
            gas_fee: None,
            block_number: None,
            error_message: None,
            retry_count: 0,
            created_at: chrono::Utc::now().timestamp() as u64,
            updated_at: chrono::Utc::now().timestamp() as u64,
            completed_at: Some(chrono::Utc::now().timestamp() as u64),
        };

        // Try to insert, but don't fail if it already exists
        match collection.insert_one(&admin_fee_transaction, None).await {
            Ok(_) => {
                info!("✅ Created admin fee transaction record for trade: {:?}", trade.id);
            }
            Err(e) => {
                warn!("⚠️ Failed to create admin fee transaction record (may already exist): {}", e);
            }
        }

        Ok(())
    }

    /// Get native symbol for blockchain
    fn get_native_symbol(&self, blockchain: &Blockchain) -> String {
        match blockchain {
            Blockchain::SOL => "SOL".to_string(),
            Blockchain::ETH => "ETH".to_string(),
            Blockchain::BSC => "BNB".to_string(),
            Blockchain::BASE => "ETH".to_string(),
        }
    }

    /// Collect Solana admin fee using high-performance methods with parallel execution
    async fn collect_solana_admin_fee(
        &self,
        trade: &Trade,
        fee_calculation: &FeeCalculationResult,
    ) -> Result<AdminFeeCollectionResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("🔥 High-performance Solana admin fee collection starting for trade: {:?}", trade.id);

        // Mark as processing immediately to prevent race conditions
        self.update_fee_status(trade, "processing", None).await?;

        // Get user's wallet keypair
        let user_keypair = self.get_user_solana_keypair(&trade.user_id).await?;

        // Get admin wallet address from settings
        let admin_address = self.get_solana_admin_address().await?;

        // Convert fee amount to lamports
        let fee_lamports = (fee_calculation.fee_amount * 1_000_000_000.0) as u64;

        info!("Transferring {} lamports ({} SOL) to admin address: {}",
              fee_lamports, fee_calculation.fee_amount, admin_address);

        // Create Solana service instance
        let solana_service = SolanaTraderService::new(
            "https://api.mainnet-beta.solana.com",
            None,
            10
        );

        // Execute both high-performance and fallback transfers in parallel
        // This ensures we have the best chance of success even if one method is slower
        let high_perf_future = solana_service.execute_high_performance_sol_transfer(
            &user_keypair,
            &admin_address,
            fee_lamports,
        );

        let fallback_future = async {
            // Wait a bit before trying fallback to give high-performance a chance
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            solana_service.transfer_sol_to_address(
                &user_keypair,
                &admin_address,
                fee_lamports,
            ).await
        };

        // Use select to get the first successful result
        let result = tokio::select! {
            high_perf_result = high_perf_future => {
                match high_perf_result {
                    Ok(signature) => {
                        info!("✅ High-performance SOL transfer successful: {}", signature);
                        Ok((signature.to_string(), "high_performance_transfer".to_string()))
                    }
                    Err(e) => {
                        warn!("⚠️ High-performance transfer failed: {}", e);
                        // Try fallback immediately if high-perf fails
                        match solana_service.transfer_sol_to_address(&user_keypair, &admin_address, fee_lamports).await {
                            Ok(signature) => {
                                info!("✅ Immediate fallback SOL transfer successful: {}", signature);
                                Ok((signature.to_string(), "immediate_fallback_transfer".to_string()))
                            }
                            Err(fallback_error) => Err(fallback_error.into())
                        }
                    }
                }
            }
            fallback_result = fallback_future => {
                match fallback_result {
                    Ok(signature) => {
                        info!("✅ Parallel fallback SOL transfer successful: {}", signature);
                        Ok((signature.to_string(), "parallel_fallback_transfer".to_string()))
                    }
                    Err(e) => {
                        warn!("⚠️ Parallel fallback transfer failed: {}", e);
                        Err(e.into())
                    }
                }
            }
        };

        match result {
            Ok((signature, method)) => {
                Ok(AdminFeeCollectionResult {
                    success: true,
                    transaction_hash: Some(signature),
                    fee_amount: fee_calculation.fee_amount,
                    fee_amount_formatted: fee_calculation.fee_amount_formatted.clone(),
                    fee_usd_value: fee_calculation.fee_usd_value,
                    blockchain: fee_calculation.blockchain.clone(),
                    native_symbol: fee_calculation.native_symbol.clone(),
                    collection_method: method,
                    error_message: None,
                    skipped_reason: None,
                })
            }
            Err(e) => {
                error!("❌ All SOL transfer methods failed for trade {:?}: {}", trade.id, e);
                Err(e)
            }
        }
    }

    /// Collect EVM admin fee using high-performance methods with parallel execution
    async fn collect_evm_admin_fee(
        &self,
        trade: &Trade,
        fee_calculation: &FeeCalculationResult,
    ) -> Result<AdminFeeCollectionResult, Box<dyn std::error::Error + Send + Sync>> {
        info!("🔥 High-performance EVM admin fee collection starting for trade: {:?}", trade.id);

        // Mark as processing immediately to prevent race conditions
        self.update_fee_status(trade, "processing", None).await?;

        // Get user's wallet private key
        let user_private_key = self.get_user_evm_private_key(&trade.user_id).await?;

        // Get admin wallet address from settings
        let admin_address = self.get_evm_admin_address(&trade.blockchain).await?;

        // Convert fee amount to wei (for ETH/BNB)
        let fee_wei = (fee_calculation.fee_amount * 1e18) as u64;

        info!("Transferring {} wei ({} {}) to admin address: {}",
              fee_wei, fee_calculation.fee_amount, fee_calculation.native_symbol, admin_address);

        // Create EVM service instance
        let evm_service = EvmTraderService::new();

        // Execute both high-performance and fallback transfers in parallel
        let high_perf_future = evm_service.execute_high_performance_native_transfer(
            &trade.blockchain,
            &user_private_key,
            &admin_address,
            fee_wei,
        );

        let fallback_future = async {
            // Wait a bit before trying fallback to give high-performance a chance
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            evm_service.transfer_native_token(
                &trade.blockchain,
                &user_private_key,
                &admin_address,
                fee_wei,
            ).await
        };

        // Use select to get the first successful result
        let result = tokio::select! {
            high_perf_result = high_perf_future => {
                match high_perf_result {
                    Ok(tx_hash) => {
                        info!("✅ High-performance EVM transfer successful: {}", tx_hash);
                        Ok((tx_hash, "high_performance_transfer".to_string()))
                    }
                    Err(e) => {
                        warn!("⚠️ High-performance EVM transfer failed: {}", e);
                        // Try fallback immediately if high-perf fails
                        match evm_service.transfer_native_token(&trade.blockchain, &user_private_key, &admin_address, fee_wei).await {
                            Ok(tx_hash) => {
                                info!("✅ Immediate fallback EVM transfer successful: {}", tx_hash);
                                Ok((tx_hash, "immediate_fallback_transfer".to_string()))
                            }
                            Err(fallback_error) => Err(fallback_error.into())
                        }
                    }
                }
            }
            fallback_result = fallback_future => {
                match fallback_result {
                    Ok(tx_hash) => {
                        info!("✅ Parallel fallback EVM transfer successful: {}", tx_hash);
                        Ok((tx_hash, "parallel_fallback_transfer".to_string()))
                    }
                    Err(e) => {
                        warn!("⚠️ Parallel fallback EVM transfer failed: {}", e);
                        Err(e.into())
                    }
                }
            }
        };

        match result {
            Ok((tx_hash, method)) => {
                Ok(AdminFeeCollectionResult {
                    success: true,
                    transaction_hash: Some(tx_hash),
                    fee_amount: fee_calculation.fee_amount,
                    fee_amount_formatted: fee_calculation.fee_amount_formatted.clone(),
                    fee_usd_value: fee_calculation.fee_usd_value,
                    blockchain: fee_calculation.blockchain.clone(),
                    native_symbol: fee_calculation.native_symbol.clone(),
                    collection_method: method,
                    error_message: None,
                    skipped_reason: None,
                })
            }
            Err(e) => {
                error!("❌ All EVM transfer methods failed for trade {:?}: {}", trade.id, e);
                Err(e)
            }
        }
    }

    /// Get user's Solana keypair from wallet database
    async fn get_user_solana_keypair(&self, user_id: &ObjectId) -> Result<Keypair, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::Wallet>("wallets");

        // Find user's Solana wallet
        let wallet = collection
            .find_one(doc! {
                "user_id": user_id,
                "blockchain": "SOL"
            }, None)
            .await?
            .ok_or("User has no Solana wallet")?;

        // Get private key from wallet
        let private_key = wallet.private_key;

        // Convert hex private key to bytes for Solana keypair
        let private_key_bytes = if private_key.starts_with("0x") {
            hex::decode(&private_key[2..])
                .map_err(|e| format!("Invalid hex private key: {}", e))?
        } else {
            // Assume it's already in the correct format
            private_key.as_bytes().to_vec()
        };

        let keypair = Keypair::from_bytes(&private_key_bytes)
            .map_err(|e| format!("Invalid Solana private key: {}", e))?;

        Ok(keypair)
    }

    /// Get user's EVM private key from wallet database
    async fn get_user_evm_private_key(&self, user_id: &ObjectId) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::Wallet>("wallets");

        // Find user's EVM wallet (try ETH first, then BSC, then BASE)
        let blockchains = ["ETH", "BSC", "BASE"];

        for blockchain in &blockchains {
            if let Ok(Some(wallet)) = collection
                .find_one(doc! {
                    "user_id": user_id,
                    "blockchain": blockchain
                }, None)
                .await
            {
                return Ok(wallet.private_key);
            }
        }

        Err("User has no EVM wallet".into())
    }

    /// Get Solana admin address from settings
    async fn get_solana_admin_address(&self) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::AdminSettings>("admin_settings");

        let settings = collection
            .find_one(doc! {}, None)
            .await?
            .ok_or("Admin settings not found")?;

        settings.admin_wallet_sol
            .ok_or("Solana admin wallet not configured".into())
    }

    /// Get EVM admin address from settings
    async fn get_evm_admin_address(&self, blockchain: &Blockchain) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
        let db = DbService::get_db();
        let collection = db.collection::<crate::model::AdminSettings>("admin_settings");

        let settings = collection
            .find_one(doc! {}, None)
            .await?
            .ok_or("Admin settings not found")?;

        let admin_wallet = match blockchain {
            Blockchain::ETH => settings.admin_wallet_eth,
            Blockchain::BSC => settings.admin_wallet_bsc,
            Blockchain::BASE => settings.admin_wallet_base,
            Blockchain::SOL => return Err("Use get_solana_admin_address for SOL".into()),
        };

        admin_wallet.ok_or(format!("{:?} admin wallet not configured", blockchain).into())
    }
}
