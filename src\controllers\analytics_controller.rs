use axum::{
    routing::{get, post},
    Router,
    extract::{Json, Path, Query, State},
    http::{StatusCode, HeaderMap},
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use mongodb::{
    bson::{doc, oid::ObjectId, DateTime as BsonDateTime},
    Collection,
    options::{FindOptions, AggregateOptions},
    error::Error as MongoError,
};
use futures::stream::TryStreamExt;
use std::{
    sync::Arc,
    collections::HashMap,
    time::{SystemTime, UNIX_EPOCH, Duration},
};
use tokio::time::timeout;
use tracing::{error, warn, info, debug};
use anyhow::{Result, Context, anyhow};
use crate::model::{
    AdminDashboard, DashboardAnalytics, SystemHealth, SystemAlert,
    AlertSeverity, BotError, HourlyStats, CachedPrice
};
use crate::service::{db_service::DbService, cached_price_service::CachedPriceService};
use crate::config::AppConfig;
use crate::controllers::auth_controller::Claims;
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};

// Constants for production analytics
const DB_OPERATION_TIMEOUT: Duration = Duration::from_secs(30);
const MAX_HOURLY_STATS: usize = 168; // 7 days worth of hourly data
const MAX_RECENT_TRANSACTIONS: i64 = 50;
const CACHE_TTL_SECONDS: u64 = 300; // 5 minutes cache

#[derive(Debug, Deserialize)]
pub struct AnalyticsQueryParams {
    pub start_date: Option<String>,
    pub end_date: Option<String>,
    pub granularity: Option<String>, // hour, day, week, month
    pub include_cache: Option<bool>,
}

#[derive(Debug, thiserror::Error)]
pub enum AnalyticsError {
    #[error("Database operation failed: {0}")]
    DatabaseError(#[from] MongoError),
    #[error("Authentication failed: {0}")]
    AuthError(String),
    #[error("Invalid query parameters: {0}")]
    InvalidParams(String),
    #[error("Operation timeout")]
    Timeout,
    #[error("Data aggregation failed: {0}")]
    AggregationError(String),
    #[error("System health check failed: {0}")]
    HealthCheckError(String),
}

#[derive(Debug, Serialize)]
pub struct DashboardResponse {
    pub analytics: DashboardAnalytics,
    pub system_health: SystemHealth,
    pub alerts: Vec<SystemAlert>,
    pub bots_summary: Vec<BotSummaryItem>,
    pub recent_transactions: Vec<TransactionSummaryItem>,
}

#[derive(Debug, Serialize)]
pub struct BotSummaryItem {
    pub id: String,
    pub name: String,
    pub bot_type: String,
    pub status: String,
    pub active_users: i64,
    pub transactions_24h: i64,
    pub volume_24h: f64,
    pub success_rate: f64,
    pub uptime_percentage: f64,
}

#[derive(Debug, Serialize)]
pub struct TransactionSummaryItem {
    pub id: String,
    pub user_id: String,
    pub bot_type: String,
    pub amount: f64,
    pub token_symbol: String,
    pub status: String,
    pub timestamp: u64,
    pub blockchain: String,
}

pub struct AnalyticsController {
    config: Arc<AppConfig>,
    jwt_secret: String,
    cache: Arc<tokio::sync::RwLock<HashMap<String, (SystemTime, serde_json::Value)>>>,
}

impl AnalyticsController {
    pub fn new(config: Arc<AppConfig>) -> Self {
        let jwt_secret = std::env::var("JWT_SECRET")
            .context("JWT_SECRET environment variable is required")
            .expect("Critical configuration missing");

        Self {
            config,
            jwt_secret,
            cache: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        }
    }

    /// Check if cached data is still valid
    async fn get_cached_data(&self, key: &str) -> Option<serde_json::Value> {
        let cache = self.cache.read().await;
        if let Some((timestamp, data)) = cache.get(key) {
            let elapsed = SystemTime::now()
                .duration_since(*timestamp)
                .unwrap_or(Duration::from_secs(u64::MAX));

            if elapsed.as_secs() < CACHE_TTL_SECONDS {
                debug!("Cache hit for key: {}", key);
                return Some(data.clone());
            }
        }
        None
    }

    /// Store data in cache
    async fn set_cached_data(&self, key: String, data: serde_json::Value) {
        let mut cache = self.cache.write().await;
        cache.insert(key, (SystemTime::now(), data));

        // Clean old entries (simple cleanup)
        let now = SystemTime::now();
        cache.retain(|_, (timestamp, _)| {
            now.duration_since(*timestamp)
                .map(|d| d.as_secs() < CACHE_TTL_SECONDS * 2)
                .unwrap_or(false)
        });
    }

    pub fn create_router(&self) -> Router {
        let controller = Arc::new(self.clone());
        let routes = &self.config.api_routes;

        Router::new()
            .route(&routes.admin_dashboard, get(Self::get_dashboard))
            .route(&routes.admin_analytics, get(Self::get_analytics))
            .route(&routes.admin_blockchain_analytics, get(Self::get_blockchain_analytics))
            .route(&routes.admin_system_health, get(Self::get_system_health))
            .route(&routes.admin_alerts, get(Self::get_alerts))
            .route(&format!("{}/:id/resolve", routes.admin_alerts), post(Self::resolve_alert))
            .route("/admin/transaction-metrics", get(Self::get_transaction_metrics))
            .route("/admin/user-metrics", get(Self::get_user_metrics))
            .route("/admin/failed-fees", get(Self::get_failed_fee_transactions))
            .route("/admin/cached-prices", get(Self::get_cached_prices))
            .with_state(controller)
    }

    async fn get_dashboard(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        let start_time = SystemTime::now();

        // Verify authentication first
        let claims = match controller.verify_auth_header(&headers).await {
            Some(claims) => claims,
            None => {
                warn!("Unauthorized dashboard access attempt");
                return (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                    "error": "Unauthorized access",
                    "code": "AUTH_REQUIRED"
                }))).into_response();
            }
        };

        info!("Dashboard data requested by admin: {}", claims.sub);

        // Check cache first
        let cache_key = "dashboard_full";
        if let Some(cached_data) = controller.get_cached_data(cache_key).await {
            debug!("Returning cached dashboard data");
            return (StatusCode::OK, Json(cached_data)).into_response();
        }

        // Fetch all dashboard components concurrently
        let analytics_future = controller.calculate_dashboard_analytics();
        let system_health_future = controller.get_current_system_health();
        let alerts_future = controller.get_current_alerts();
        let bots_summary_future = controller.get_bots_summary();
        let recent_transactions_future = controller.get_recent_transactions();

        let results = tokio::join!(
            analytics_future,
            system_health_future,
            alerts_future,
            bots_summary_future,
            recent_transactions_future
        );

        match (results.0, results.1) {
            (Ok(analytics), Ok(system_health)) => {
                let alerts = results.2;
                let bots_summary = results.3;
                let recent_transactions = results.4;
                let dashboard = DashboardResponse {
                    analytics,
                    system_health,
                    alerts,
                    bots_summary,
                    recent_transactions,
                };

                // Cache the result
                if let Ok(json_data) = serde_json::to_value(&dashboard) {
                    controller.set_cached_data(cache_key.to_string(), json_data).await;
                }

                let elapsed = start_time.elapsed().unwrap_or_default();
                info!("Dashboard data generated in {:?}", elapsed);

                (StatusCode::OK, Json(dashboard)).into_response()
            }
            (Err(e), _) | (_, Err(e)) => {
                error!("Failed to generate dashboard data: {:?}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                    "error": "Failed to generate dashboard data",
                    "code": "DASHBOARD_ERROR",
                    "details": e.to_string()
                }))).into_response()
            }
        }
    }

    async fn get_analytics(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
        Query(params): Query<AnalyticsQueryParams>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.calculate_dashboard_analytics().await {
                Ok(analytics) => (StatusCode::OK, Json(analytics)).into_response(),
                Err(e) => {
                    error!("Failed to calculate analytics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to calculate analytics",
                        "code": "ANALYTICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_blockchain_analytics(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_detailed_blockchain_analytics().await {
                Ok(blockchain_analytics) => (StatusCode::OK, Json(blockchain_analytics)).into_response(),
                Err(e) => {
                    error!("Failed to get blockchain analytics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get blockchain analytics",
                        "code": "BLOCKCHAIN_ANALYTICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_system_health(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.get_current_system_health().await {
                Ok(system_health) => (StatusCode::OK, Json(system_health)).into_response(),
                Err(e) => {
                    error!("Failed to get system health: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to get system health",
                        "code": "HEALTH_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_alerts(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            let alerts = controller.get_current_alerts().await;
            (StatusCode::OK, Json(alerts)).into_response()
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn resolve_alert(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
        Path(id): Path<String>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            // This would implement alert resolution
            // For now, return success
            (StatusCode::OK, Json(serde_json::json!({
                "message": format!("Alert {} resolved successfully", id)
            })))
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            })))
        }
    }

    async fn get_transaction_metrics(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
        Query(_params): Query<AnalyticsQueryParams>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.calculate_detailed_transaction_metrics().await {
                Ok(metrics) => (StatusCode::OK, Json(metrics)).into_response(),
                Err(e) => {
                    error!("Failed to calculate transaction metrics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to calculate transaction metrics",
                        "code": "TRANSACTION_METRICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_user_metrics(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
        Query(_params): Query<AnalyticsQueryParams>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.calculate_detailed_user_metrics().await {
                Ok(metrics) => (StatusCode::OK, Json(metrics)).into_response(),
                Err(e) => {
                    error!("Failed to calculate user metrics: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to calculate user metrics",
                        "code": "USER_METRICS_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_failed_fee_transactions(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
        Query(_params): Query<AnalyticsQueryParams>,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            match controller.fetch_failed_fee_transactions().await {
                Ok(failed_fees) => (StatusCode::OK, Json(failed_fees)).into_response(),
                Err(e) => {
                    error!("Failed to fetch failed fee transactions: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to fetch failed fee transactions",
                        "code": "FAILED_FEES_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    async fn get_cached_prices(
        State(controller): State<Arc<AnalyticsController>>,
        headers: HeaderMap,
    ) -> impl IntoResponse {
        if let Some(_claims) = controller.verify_auth_header(&headers).await {
            let cached_price_service = CachedPriceService::new();
            match cached_price_service.get_all_cached_prices().await {
                Ok(cached_prices) => {
                    let response = serde_json::json!({
                        "cached_prices": cached_prices,
                        "total_count": cached_prices.len(),
                        "last_updated": std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_secs()
                    });
                    (StatusCode::OK, Json(response)).into_response()
                }
                Err(e) => {
                    error!("Failed to fetch cached prices: {:?}", e);
                    (StatusCode::INTERNAL_SERVER_ERROR, Json(serde_json::json!({
                        "error": "Failed to fetch cached prices",
                        "code": "CACHED_PRICES_ERROR"
                    }))).into_response()
                }
            }
        } else {
            (StatusCode::UNAUTHORIZED, Json(serde_json::json!({
                "error": "Unauthorized"
            }))).into_response()
        }
    }

    // Helper methods for data calculation
    async fn calculate_dashboard_analytics(&self) -> Result<DashboardAnalytics, AnalyticsError> {
        let db = DbService::get_db();

        // Get current timestamp and 24h ago timestamp
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| AnalyticsError::AggregationError("System time error".to_string()))?
            .as_secs();
        let twenty_four_hours_ago = now - (24 * 60 * 60);

        info!("Calculating dashboard analytics for period: {} to {}", twenty_four_hours_ago, now);

        // Execute all aggregations concurrently with timeout
        let user_stats_future = self.calculate_user_statistics(twenty_four_hours_ago);
        let transaction_stats_future = self.calculate_transaction_statistics(twenty_four_hours_ago);
        let volume_stats_future = self.calculate_volume_statistics(twenty_four_hours_ago);
        let performance_stats_future = self.calculate_performance_statistics();
        let blockchain_dist_future = self.calculate_blockchain_distribution();
        let hourly_stats_future = self.generate_hourly_statistics(twenty_four_hours_ago);
        let admin_fees_future = self.calculate_admin_fees_collected(twenty_four_hours_ago);

        let results = timeout(
            DB_OPERATION_TIMEOUT,
            async {
                tokio::try_join!(
                    user_stats_future,
                    transaction_stats_future,
                    volume_stats_future,
                    performance_stats_future,
                    blockchain_dist_future,
                    hourly_stats_future,
                    admin_fees_future
                )
            }
        ).await
        .map_err(|_| AnalyticsError::Timeout)?;

        match results {
            Ok((user_stats, tx_stats, volume_stats, perf_stats, blockchain_dist, hourly_stats, admin_fees)) => {
                Ok(DashboardAnalytics {
                    total_users: user_stats.0,
                    active_users_24h: user_stats.1,
                    total_transactions: tx_stats.0,
                    transactions_24h: tx_stats.1,
                    total_volume: volume_stats.0,
                    volume_24h: volume_stats.1,
                    total_fees_collected: admin_fees.0,
                    fees_collected_24h: admin_fees.1,
                    success_rate: perf_stats.0,
                    average_response_time: perf_stats.1,
                    blockchain_distribution: blockchain_dist,
                    hourly_stats,
                })
            }
            Err(e) => {
                error!("Analytics calculation failed: {:?}", e);
                Err(AnalyticsError::AggregationError(e.to_string()))
            }
        }
    }

    async fn calculate_user_statistics(&self, twenty_four_hours_ago: u64) -> Result<(i64, i64), AnalyticsError> {
        let db = DbService::get_db();
        let users_collection = db.collection::<mongodb::bson::Document>("users");

        let total_users_future = users_collection.count_documents(doc! {}, None);
        let active_users_future = users_collection.count_documents(
            doc! {
                "last_activity": { "$gte": twenty_four_hours_ago as i64 }
            },
            None
        );

        let (total_users, active_users_24h) = tokio::try_join!(
            total_users_future,
            active_users_future
        )?;

        debug!("User stats - Total: {}, Active 24h: {}", total_users, active_users_24h);
        Ok((total_users as i64, active_users_24h as i64))
    }

    async fn calculate_transaction_statistics(&self, twenty_four_hours_ago: u64) -> Result<(i64, i64), AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        // Calculate total transactions
        let total_tx_future = trades_collection.count_documents(doc! {}, None);

        // Calculate recent transactions (24h)
        let recent_tx_future = trades_collection.count_documents(
            doc! {
                "created_at": { "$gte": twenty_four_hours_ago as i64 }
            },
            None
        );

        // Also calculate completed and pending transactions for better metrics
        let completed_tx_future = trades_collection.count_documents(
            doc! {
                "status": "completed"
            },
            None
        );

        let pending_tx_future = trades_collection.count_documents(
            doc! {
                "status": "pending"
            },
            None
        );

        let (total_transactions, transactions_24h, _completed_transactions, _pending_transactions) = tokio::try_join!(
            total_tx_future,
            recent_tx_future,
            completed_tx_future,
            pending_tx_future
        )?;

        debug!("Transaction stats - Total: {}, 24h: {}, Completed: {}, Pending: {}",
               total_transactions, transactions_24h, _completed_transactions, _pending_transactions);
        Ok((total_transactions as i64, transactions_24h as i64))
    }

    async fn calculate_volume_statistics(&self, twenty_four_hours_ago: u64) -> Result<(f64, f64, f64, f64), AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        // Aggregate total volume and fees - use amount_out field from Trade model
        let total_volume_pipeline = vec![
            doc! {
                "$group": {
                    "_id": null,
                    "total_volume": { "$sum": "$amount_out" },
                    "total_fees": { "$sum": "$admin_fee_amount" }
                }
            }
        ];

        // Aggregate 24h volume and fees
        let volume_24h_pipeline = vec![
            doc! {
                "$match": {
                    "timestamp": { "$gte": twenty_four_hours_ago as i64 }
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "volume_24h": { "$sum": "$amount_out" },
                    "fees_24h": { "$sum": "$admin_fee_amount" }
                }
            }
        ];

        let total_future = trades_collection.aggregate(total_volume_pipeline, None);
        let recent_future = trades_collection.aggregate(volume_24h_pipeline, None);

        let (mut total_cursor, mut recent_cursor) = tokio::try_join!(total_future, recent_future)?;

        let (total_volume, total_fees_collected) = if let Some(result) = total_cursor.try_next().await? {
            (
                result.get_f64("total_volume").unwrap_or(0.0),
                result.get_f64("total_fees").unwrap_or(0.0)
            )
        } else {
            (0.0, 0.0)
        };

        let (volume_24h, fees_collected_24h) = if let Some(result) = recent_cursor.try_next().await? {
            (
                result.get_f64("volume_24h").unwrap_or(0.0),
                result.get_f64("fees_24h").unwrap_or(0.0)
            )
        } else {
            (0.0, 0.0)
        };

        debug!("Volume stats - Total: {}, 24h: {}, Fees total: {}, 24h: {}",
               total_volume, volume_24h, total_fees_collected, fees_collected_24h);

        Ok((total_volume, volume_24h, total_fees_collected, fees_collected_24h))
    }

    async fn calculate_performance_statistics(&self) -> Result<(f64, f64), AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");
        let bots_collection = db.collection::<mongodb::bson::Document>("bots");

        // Calculate success rate
        let success_rate_pipeline = vec![
            doc! {
                "$group": {
                    "_id": null,
                    "total": { "$sum": 1 },
                    "successful": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$status", "completed"] },
                                1,
                                0
                            ]
                        }
                    }
                }
            },
            doc! {
                "$project": {
                    "success_rate": {
                        "$cond": [
                            { "$gt": ["$total", 0] },
                            { "$multiply": [{ "$divide": ["$successful", "$total"] }, 100] },
                            0
                        ]
                    }
                }
            }
        ];

        // Calculate average response time from bot performance data
        let avg_response_pipeline = vec![
            doc! {
                "$group": {
                    "_id": null,
                    "avg_response_time": { "$avg": "$average_response_time" }
                }
            }
        ];

        let success_future = trades_collection.aggregate(success_rate_pipeline, None);
        let response_future = bots_collection.aggregate(avg_response_pipeline, None);

        let (mut success_cursor, mut response_cursor) = tokio::try_join!(success_future, response_future)?;

        let success_rate = if let Some(result) = success_cursor.try_next().await? {
            result.get_f64("success_rate").unwrap_or(0.0)
        } else {
            0.0
        };

        let average_response_time = if let Some(result) = response_cursor.try_next().await? {
            result.get_f64("avg_response_time").unwrap_or(0.0)
        } else {
            0.0
        };

        debug!("Performance stats - Success rate: {}%, Avg response time: {}ms", success_rate, average_response_time);
        Ok((success_rate, average_response_time))
    }

    async fn calculate_blockchain_distribution(&self) -> Result<HashMap<String, i64>, AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        // Enhanced blockchain analytics pipeline
        let blockchain_pipeline = vec![
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "total_transactions": { "$sum": 1 },
                    "total_volume": { "$sum": "$amount_out" },
                    "successful_transactions": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$status", "completed"] },
                                1,
                                0
                            ]
                        }
                    },
                    "unique_users": { "$addToSet": "$user_id" },
                    "avg_amount": { "$avg": "$amount_out" },
                    "total_fees": { "$sum": "$admin_fee_amount" }
                }
            },
            doc! {
                "$project": {
                    "blockchain": "$_id",
                    "total_transactions": 1,
                    "total_volume": 1,
                    "successful_transactions": 1,
                    "unique_users_count": { "$size": "$unique_users" },
                    "avg_amount": 1,
                    "total_fees": 1,
                    "success_rate": {
                        "$cond": [
                            { "$gt": ["$total_transactions", 0] },
                            { "$multiply": [{ "$divide": ["$successful_transactions", "$total_transactions"] }, 100] },
                            0
                        ]
                    }
                }
            },
            doc! {
                "$sort": { "total_transactions": -1 }
            }
        ];

        let mut cursor = trades_collection.aggregate(blockchain_pipeline, None).await?;
        let mut blockchain_distribution = HashMap::new();

        // For backward compatibility, we'll still return the simple count format
        // but we'll also store the detailed data for the new blockchain analytics endpoint
        while let Some(result) = cursor.try_next().await? {
            if let (Ok(blockchain), Ok(count)) = (result.get_str("blockchain"), result.get_i64("total_transactions")) {
                blockchain_distribution.insert(blockchain.to_string(), count);
            }
        }

        // Ensure all supported blockchains are represented
        let supported_blockchains = ["SOL", "BNB", "ETH", "BASE"];
        for blockchain in supported_blockchains {
            blockchain_distribution.entry(blockchain.to_string()).or_insert(0);
        }

        debug!("Blockchain distribution: {:?}", blockchain_distribution);
        Ok(blockchain_distribution)
    }

    async fn generate_hourly_statistics(&self, start_time: u64) -> Result<Vec<HourlyStats>, AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        // Use a single aggregation pipeline for all hourly data
        let pipeline = vec![
            doc! {
                "$match": {
                    "timestamp": {
                        "$gte": start_time as i64,
                        "$lt": (start_time + 24 * 3600) as i64
                    }
                }
            },
            doc! {
                "$addFields": {
                    "hour_bucket": {
                        "$floor": {
                            "$divide": [
                                { "$subtract": ["$timestamp", start_time as i64] },
                                3600
                            ]
                        }
                    }
                }
            },
            doc! {
                "$group": {
                    "_id": "$hour_bucket",
                    "transactions": { "$sum": 1 },
                    "volume": { "$sum": "$amount_out" },
                    "unique_users": { "$addToSet": "$user_id" },
                    "successful": {
                        "$sum": {
                            "$cond": [
                                { "$eq": ["$status", "completed"] },
                                1,
                                0
                            ]
                        }
                    }
                }
            },
            doc! {
                "$project": {
                    "hour": "$_id",
                    "transactions": 1,
                    "volume": 1,
                    "users": { "$size": "$unique_users" },
                    "success_rate": {
                        "$cond": [
                            { "$gt": ["$transactions", 0] },
                            { "$multiply": [{ "$divide": ["$successful", "$transactions"] }, 100] },
                            0
                        ]
                    }
                }
            },
            doc! {
                "$sort": { "hour": 1 }
            }
        ];

        let mut cursor = trades_collection.aggregate(pipeline, None).await?;
        let mut hourly_data: HashMap<i64, HourlyStats> = HashMap::new();

        // Collect actual data
        while let Some(result) = cursor.try_next().await? {
            if let Ok(hour) = result.get_i64("hour") {
                let stats = HourlyStats {
                    hour: hour as u64,
                    transactions: result.get_i64("transactions").unwrap_or(0),
                    volume: result.get_f64("volume").unwrap_or(0.0),
                    users: result.get_i64("users").unwrap_or(0),
                    success_rate: result.get_f64("success_rate").unwrap_or(0.0),
                };
                hourly_data.insert(hour, stats);
            }
        }

        // Fill in missing hours with zero data
        let mut hourly_stats = Vec::with_capacity(24);
        for hour in 0..24 {
            if let Some(stats) = hourly_data.remove(&hour) {
                hourly_stats.push(stats);
            } else {
                hourly_stats.push(HourlyStats {
                    hour: hour as u64,
                    transactions: 0,
                    volume: 0.0,
                    users: 0,
                    success_rate: 0.0,
                });
            }
        }

        debug!("Generated {} hourly statistics", hourly_stats.len());
        Ok(hourly_stats)
    }

    async fn calculate_detailed_transaction_metrics(&self) -> Result<serde_json::Value, AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| AnalyticsError::AggregationError("System time error".to_string()))?
            .as_secs();
        let twenty_four_hours_ago = now - (24 * 60 * 60);

        // Detailed transaction metrics pipeline
        let metrics_pipeline = vec![
            doc! {
                "$facet": {
                    "total_stats": [
                        {
                            "$group": {
                                "_id": null,
                                "total_transactions": { "$sum": 1 },
                                "completed_transactions": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "completed"] }, 1, 0]
                                    }
                                },
                                "pending_transactions": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "pending"] }, 1, 0]
                                    }
                                },
                                "failed_transactions": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "failed"] }, 1, 0]
                                    }
                                },
                                "total_fees": { "$sum": "$admin_fee_amount" }
                            }
                        }
                    ],
                    "recent_stats": [
                        {
                            "$match": {
                                "created_at": { "$gte": twenty_four_hours_ago as i64 }
                            }
                        },
                        {
                            "$group": {
                                "_id": null,
                                "transactions_24h": { "$sum": 1 },
                                "completed_24h": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "completed"] }, 1, 0]
                                    }
                                },
                                "pending_24h": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "pending"] }, 1, 0]
                                    }
                                },
                                "failed_24h": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "failed"] }, 1, 0]
                                    }
                                },
                                "fees_24h": { "$sum": "$admin_fee_amount" }
                            }
                        }
                    ],
                    "blockchain_breakdown": [
                        {
                            "$group": {
                                "_id": "$blockchain",
                                "count": { "$sum": 1 },
                                "completed": {
                                    "$sum": {
                                        "$cond": [{ "$eq": ["$status", "completed"] }, 1, 0]
                                    }
                                }
                            }
                        }
                    ]
                }
            }
        ];

        let mut cursor = trades_collection.aggregate(metrics_pipeline, None).await?;

        if let Some(result) = cursor.try_next().await? {
            Ok(serde_json::json!({
                "total_stats": result.get_array("total_stats").unwrap_or(&vec![]).get(0),
                "recent_stats": result.get_array("recent_stats").unwrap_or(&vec![]).get(0),
                "blockchain_breakdown": result.get_array("blockchain_breakdown").unwrap_or(&vec![])
            }))
        } else {
            Ok(serde_json::json!({
                "total_stats": {},
                "recent_stats": {},
                "blockchain_breakdown": []
            }))
        }
    }

    async fn calculate_detailed_user_metrics(&self) -> Result<serde_json::Value, AnalyticsError> {
        let db = DbService::get_db();
        let users_collection = db.collection::<mongodb::bson::Document>("users");
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| AnalyticsError::AggregationError("System time error".to_string()))?
            .as_secs();
        let twenty_four_hours_ago = now - (24 * 60 * 60);
        let seven_days_ago = now - (7 * 24 * 60 * 60);
        let thirty_days_ago = now - (30 * 24 * 60 * 60);

        // User metrics pipeline
        let user_stats_future = users_collection.aggregate(vec![
            doc! {
                "$facet": {
                    "total_users": [
                        { "$count": "count" }
                    ],
                    "active_24h": [
                        {
                            "$match": {
                                "last_activity": { "$gte": twenty_four_hours_ago as i64 }
                            }
                        },
                        { "$count": "count" }
                    ],
                    "active_7d": [
                        {
                            "$match": {
                                "last_activity": { "$gte": seven_days_ago as i64 }
                            }
                        },
                        { "$count": "count" }
                    ],
                    "active_30d": [
                        {
                            "$match": {
                                "last_activity": { "$gte": thirty_days_ago as i64 }
                            }
                        },
                        { "$count": "count" }
                    ]
                }
            }
        ], None);

        // Top users by transaction volume
        let top_users_future = trades_collection.aggregate(vec![
            doc! {
                "$group": {
                    "_id": "$user_id",
                    "transaction_count": { "$sum": 1 },
                    "total_volume": { "$sum": "$amount_out" },
                    "total_fees_paid": { "$sum": "$admin_fee_amount" }
                }
            },
            doc! {
                "$sort": { "total_volume": -1 }
            },
            doc! {
                "$limit": 10
            }
        ], None);

        let (mut user_cursor, mut top_users_cursor) = tokio::try_join!(user_stats_future, top_users_future)?;

        let user_stats = if let Some(result) = user_cursor.try_next().await? {
            result
        } else {
            doc! {}
        };

        let mut top_users = Vec::new();
        while let Some(user) = top_users_cursor.try_next().await? {
            top_users.push(user);
        }

        Ok(serde_json::json!({
            "user_stats": user_stats,
            "top_users": top_users
        }))
    }

    async fn fetch_failed_fee_transactions(&self) -> Result<serde_json::Value, AnalyticsError> {
        let db = DbService::get_db();
        let collection = db.collection::<mongodb::bson::Document>("admin_fee_transactions");

        // Get failed fee transactions with details
        let pipeline = vec![
            doc! {
                "$match": {
                    "status": "failed"
                }
            },
            doc! {
                "$sort": {
                    "created_at": -1
                }
            },
            doc! {
                "$limit": 100
            },
            doc! {
                "$lookup": {
                    "from": "users",
                    "localField": "user_id",
                    "foreignField": "_id",
                    "as": "user_info"
                }
            },
            doc! {
                "$project": {
                    "_id": 1,
                    "user_id": 1,
                    "blockchain": 1,
                    "transaction_type": 1,
                    "fee_percentage": 1,
                    "original_amount": 1,
                    "fee_amount": 1,
                    "fee_token_symbol": 1,
                    "fee_token_address": 1,
                    "admin_address": 1,
                    "user_wallet_address": 1,
                    "error_message": 1,
                    "retry_count": 1,
                    "created_at": 1,
                    "updated_at": 1,
                    "user_info.first_name": 1,
                    "user_info.username": 1,
                    "user_info.chat_id": 1
                }
            }
        ];

        let mut cursor = collection.aggregate(pipeline, None).await
            .map_err(|e| AnalyticsError::AggregationError(e.to_string()))?;

        let mut failed_transactions = Vec::new();
        while let Some(doc) = cursor.try_next().await
            .map_err(|e| AnalyticsError::AggregationError(e.to_string()))? {
            failed_transactions.push(doc);
        }

        // Get summary statistics
        let summary_pipeline = vec![
            doc! {
                "$match": {
                    "status": "failed"
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_failed": { "$sum": 1 },
                    "total_failed_amount": { "$sum": "$fee_amount" },
                    "by_blockchain": {
                        "$push": {
                            "blockchain": "$blockchain",
                            "amount": "$fee_amount"
                        }
                    }
                }
            }
        ];

        let mut summary_cursor = collection.aggregate(summary_pipeline, None).await
            .map_err(|e| AnalyticsError::AggregationError(e.to_string()))?;

        let summary = if let Some(doc) = summary_cursor.try_next().await
            .map_err(|e| AnalyticsError::AggregationError(e.to_string()))? {
            doc
        } else {
            doc! {
                "total_failed": 0,
                "total_failed_amount": 0.0,
                "by_blockchain": []
            }
        };

        Ok(serde_json::json!({
            "failed_transactions": failed_transactions,
            "summary": summary,
            "total_count": failed_transactions.len()
        }))
    }

    async fn get_current_system_health(&self) -> Result<SystemHealth, AnalyticsError> {
        let db = DbService::get_db();

        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map_err(|_| AnalyticsError::HealthCheckError("System time error".to_string()))?
            .as_secs();

        // Execute health checks concurrently
        let db_health_future = self.check_database_health();
        let blockchain_health_future = self.check_blockchain_connections(now);
        let api_activity_future = self.check_api_activity(now);
        let system_metrics_future = self.get_system_metrics();

        let results = timeout(
            DB_OPERATION_TIMEOUT,
            async {
                tokio::try_join!(
                    db_health_future,
                    blockchain_health_future,
                    api_activity_future,
                    system_metrics_future
                )
            }
        ).await
        .map_err(|_| AnalyticsError::Timeout)?;

        match results {
            Ok((db_health, blockchain_connections, api_requests_per_minute, system_metrics)) => {
                Ok(SystemHealth {
                    uptime_seconds: system_metrics.0,
                    cpu_usage: system_metrics.1,
                    memory_usage: system_metrics.2,
                    disk_usage: system_metrics.3,
                    active_connections: db_health.0,
                    api_requests_per_minute,
                    database_connections: db_health.1,
                    blockchain_connections,
                    last_backup: system_metrics.4,
                })
            }
            Err(e) => {
                error!("System health check failed: {:?}", e);
                Err(AnalyticsError::HealthCheckError(e.to_string()))
            }
        }
    }

    async fn check_database_health(&self) -> Result<(i64, i64), AnalyticsError> {
        let db = DbService::get_db();

        // Simple database connectivity check using a basic operation
        // This doesn't require admin privileges like serverStatus
        let users_collection = db.collection::<mongodb::bson::Document>("users");

        // Perform a simple count operation to verify database connectivity
        let _count = users_collection.estimated_document_count(None).await
            .map_err(|e| AnalyticsError::DatabaseError(e))?;

        // Since we can't get actual connection stats without admin privileges,
        // we'll return reasonable defaults for a healthy connection
        let active_connections = 1; // We know we have at least one working connection
        let database_connections = 1; // We have one main connection

        debug!("Database health check successful - Connection active");
        Ok((active_connections, database_connections))
    }

    async fn check_blockchain_connections(&self, now: u64) -> Result<HashMap<String, bool>, AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        let five_minutes_ago = now - 300;

        let blockchain_pipeline = vec![
            doc! {
                "$match": {
                    "timestamp": { "$gte": five_minutes_ago as i64 }
                }
            },
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "count": { "$sum": 1 }
                }
            }
        ];

        let mut cursor = trades_collection.aggregate(blockchain_pipeline, None).await?;
        let mut blockchain_connections = HashMap::new();

        // Initialize all supported blockchains as disconnected
        for blockchain in ["BSC", "Ethereum", "Solana", "Base"] {
            blockchain_connections.insert(blockchain.to_string(), false);
        }

        // Mark blockchains with recent activity as connected
        while let Some(result) = cursor.try_next().await? {
            if let Ok(blockchain) = result.get_str("_id") {
                blockchain_connections.insert(blockchain.to_string(), true);
            }
        }

        debug!("Blockchain connections: {:?}", blockchain_connections);
        Ok(blockchain_connections)
    }

    async fn check_api_activity(&self, now: u64) -> Result<i64, AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        let one_minute_ago = now - 60;

        let api_requests_per_minute = trades_collection.count_documents(
            doc! {
                "timestamp": { "$gte": one_minute_ago as i64 }
            },
            None
        ).await? as i64;

        debug!("API activity - Requests per minute: {}", api_requests_per_minute);
        Ok(api_requests_per_minute)
    }

    async fn get_system_metrics(&self) -> Result<(u64, f64, f64, f64, Option<u64>), AnalyticsError> {
        // In a real production system, you would integrate with system monitoring tools
        // For now, we'll return basic metrics

        let uptime_seconds = 86400 * 7; // Would track from app start time
        let cpu_usage = 0.0; // Would integrate with system monitoring
        let memory_usage = 0.0; // Would integrate with system monitoring
        let disk_usage = 0.0; // Would integrate with system monitoring
        let last_backup = None; // Would track from backup system

        Ok((uptime_seconds, cpu_usage, memory_usage, disk_usage, last_backup))
    }

    async fn get_current_alerts(&self) -> Vec<SystemAlert> {
        let db = DbService::get_db();
        let alerts_collection = db.collection::<mongodb::bson::Document>("system_alerts");

        // Get unresolved alerts from the last 24 hours
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let twenty_four_hours_ago = now - (24 * 60 * 60);

        let mut alerts = Vec::new();

        let filter = doc! {
            "resolved": false,
            "timestamp": { "$gte": twenty_four_hours_ago as i64 }
        };

        let mut cursor = alerts_collection.find(filter, None).await.unwrap();

        while let Some(result) = cursor.try_next().await.unwrap() {
            if let (Ok(id), Ok(severity_str), Ok(title), Ok(message), Ok(timestamp), Ok(component)) = (
                result.get_object_id("_id").map(|id| id.to_hex()),
                result.get_str("severity"),
                result.get_str("title"),
                result.get_str("message"),
                result.get_i64("timestamp"),
                result.get_str("component")
            ) {
                let severity = match severity_str {
                    "Info" => AlertSeverity::Info,
                    "Warning" => AlertSeverity::Warning,
                    "Error" => AlertSeverity::Error,
                    "Critical" => AlertSeverity::Critical,
                    _ => AlertSeverity::Info,
                };

                alerts.push(SystemAlert {
                    id,
                    severity,
                    title: title.to_string(),
                    message: message.to_string(),
                    timestamp: timestamp as u64,
                    resolved: result.get_bool("resolved").unwrap_or(false),
                    component: component.to_string(),
                });
            }
        }

        // Generate dynamic alerts based on recent activity (avoid recursive call)
        // Check blockchain connections directly
        if let Ok(blockchain_connections) = self.check_blockchain_connections(now).await {
            for (blockchain, connected) in &blockchain_connections {
                if !connected {
                    alerts.push(SystemAlert {
                        id: ObjectId::new().to_hex(),
                        severity: AlertSeverity::Error,
                        title: format!("{} Connection Lost", blockchain),
                        message: format!("Unable to connect to {} RPC endpoint", blockchain),
                        timestamp: now,
                        resolved: false,
                        component: "Blockchain".to_string(),
                    });
                }
            }
        }

        // Check for low API activity directly
        if let Ok(api_requests_per_minute) = self.check_api_activity(now).await {
            if api_requests_per_minute < 10 {
                alerts.push(SystemAlert {
                    id: ObjectId::new().to_hex(),
                    severity: AlertSeverity::Warning,
                    title: "Low API Activity".to_string(),
                    message: format!("Only {} API requests in the last minute", api_requests_per_minute),
                    timestamp: now,
                    resolved: false,
                    component: "API".to_string(),
                });
            }
        }

        alerts
    }

    async fn get_bots_summary(&self) -> Vec<BotSummaryItem> {
        let db = DbService::get_db();
        let bots_collection = db.collection::<mongodb::bson::Document>("bots");
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");
        let users_collection = db.collection::<mongodb::bson::Document>("users");

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let twenty_four_hours_ago = now - (24 * 60 * 60);

        let mut bots_summary = Vec::new();
        let mut cursor = bots_collection.find(doc! {}, None).await.unwrap();

        while let Some(bot_doc) = cursor.try_next().await.unwrap() {
            if let (Ok(id), Ok(name), Ok(bot_type), Ok(status)) = (
                bot_doc.get_object_id("_id").map(|id| id.to_hex()),
                bot_doc.get_str("name"),
                bot_doc.get_str("bot_type"),
                bot_doc.get_str("status")
            ) {
                // Get active users for this bot type in last 24h
                let active_users = users_collection.count_documents(
                    doc! {
                        "preferred_blockchain": bot_type,
                        "last_activity": { "$gte": twenty_four_hours_ago as i64 }
                    },
                    None
                ).await.unwrap_or(0) as i64;

                // Get transactions for this bot type in last 24h
                let transactions_24h_pipeline = vec![
                    doc! {
                        "$match": {
                            "blockchain": bot_type,
                            "timestamp": { "$gte": twenty_four_hours_ago as i64 }
                        }
                    },
                    doc! {
                        "$group": {
                            "_id": null,
                            "count": { "$sum": 1 },
                            "volume": { "$sum": "$amount" },
                            "successful": {
                                "$sum": {
                                    "$cond": [
                                        { "$eq": ["$status", "completed"] },
                                        1,
                                        0
                                    ]
                                }
                            }
                        }
                    }
                ];

                let mut tx_cursor = trades_collection.aggregate(transactions_24h_pipeline, None).await.unwrap();
                let (transactions_24h, volume_24h, success_rate) = if let Some(result) = tx_cursor.try_next().await.unwrap() {
                    let count = result.get_i64("count").unwrap_or(0);
                    let volume = result.get_f64("volume").unwrap_or(0.0);
                    let successful = result.get_i64("successful").unwrap_or(0);
                    let success_rate = if count > 0 {
                        (successful as f64 / count as f64) * 100.0
                    } else {
                        0.0
                    };
                    (count, volume, success_rate)
                } else {
                    (0, 0.0, 0.0)
                };

                let uptime_percentage = if status == "Active" { 99.5 } else { 0.0 };

                bots_summary.push(BotSummaryItem {
                    id,
                    name: name.to_string(),
                    bot_type: bot_type.to_string(),
                    status: status.to_string(),
                    active_users,
                    transactions_24h,
                    volume_24h,
                    success_rate,
                    uptime_percentage,
                });
            }
        }

        bots_summary
    }

    async fn get_recent_transactions(&self) -> Vec<TransactionSummaryItem> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");

        let mut recent_transactions = Vec::new();

        // Get the 10 most recent transactions
        let options = mongodb::options::FindOptions::builder()
            .sort(doc! { "timestamp": -1 })
            .limit(10)
            .build();

        let mut cursor = trades_collection.find(doc! {}, options).await.unwrap();

        while let Some(tx_doc) = cursor.try_next().await.unwrap() {
            if let (Ok(id), Ok(user_id), Ok(amount), Ok(status), Ok(timestamp), Ok(blockchain)) = (
                tx_doc.get_object_id("_id").map(|id| id.to_hex()),
                tx_doc.get_object_id("user_id").map(|id| id.to_hex()),
                tx_doc.get_f64("amount_out"),
                tx_doc.get_str("status"),
                tx_doc.get_i64("timestamp"),
                tx_doc.get_str("blockchain")
            ) {
                let bot_type = blockchain.to_string();
                let token_symbol = tx_doc.get_str("token_symbol").unwrap_or("UNKNOWN").to_string();

                recent_transactions.push(TransactionSummaryItem {
                    id,
                    user_id,
                    bot_type,
                    amount,
                    token_symbol,
                    status: status.to_string(),
                    timestamp: timestamp as u64,
                    blockchain: blockchain.to_string(),
                });
            }
        }

        recent_transactions
    }

    // Helper method to verify authentication
    async fn verify_auth_header(&self, headers: &HeaderMap) -> Option<Claims> {
        if let Some(auth_header) = headers.get("authorization") {
            if let Ok(auth_str) = auth_header.to_str() {
                if let Some(token) = auth_str.strip_prefix("Bearer ") {
                    return self.verify_token(token).await.ok();
                }
            }
        }
        None
    }

    async fn verify_token(&self, token: &str) -> Result<Claims, BotError> {
        decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_ref()),
            &Validation::new(Algorithm::HS256),
        )
        .map(|data| data.claims)
        .map_err(|_| BotError::InvalidToken)
    }

    // New method for detailed blockchain analytics
    async fn get_detailed_blockchain_analytics(&self) -> Result<HashMap<String, serde_json::Value>, AnalyticsError> {
        let db = DbService::get_db();
        let trades_collection = db.collection::<mongodb::bson::Document>("trades");
        let users_collection = db.collection::<mongodb::bson::Document>("users");

        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let twenty_four_hours_ago = now - (24 * 60 * 60);

        // Enhanced blockchain analytics pipeline with 24h data
        let blockchain_pipeline = vec![
            doc! {
                "$facet": {
                    "all_time": [
                        {
                            "$group": {
                                "_id": "$blockchain",
                                "total_transactions": { "$sum": 1 },
                                "total_volume": { "$sum": "$amount_out" },
                                "successful_transactions": {
                                    "$sum": {
                                        "$cond": [
                                            { "$eq": ["$status", "completed"] },
                                            1,
                                            0
                                        ]
                                    }
                                },
                                "unique_users": { "$addToSet": "$user_id" },
                                "avg_amount": { "$avg": "$amount_out" },
                                "total_fees": { "$sum": "$admin_fee_amount" },
                                "avg_response_time": { "$avg": 250.0 } // Simulated response time
                            }
                        }
                    ],
                    "last_24h": [
                        {
                            "$match": {
                                "timestamp": { "$gte": twenty_four_hours_ago as i64 }
                            }
                        },
                        {
                            "$group": {
                                "_id": "$blockchain",
                                "transactions_24h": { "$sum": 1 },
                                "volume_24h": { "$sum": "$amount_out" },
                                "successful_24h": {
                                    "$sum": {
                                        "$cond": [
                                            { "$eq": ["$status", "completed"] },
                                            1,
                                            0
                                        ]
                                    }
                                },
                                "unique_users_24h": { "$addToSet": "$user_id" },
                                "fees_24h": { "$sum": "$admin_fee_amount" }
                            }
                        }
                    ]
                }
            }
        ];

        let mut cursor = trades_collection.aggregate(blockchain_pipeline, None).await?;
        let mut blockchain_analytics = HashMap::new();

        if let Some(result) = cursor.try_next().await? {
            // Process all-time data
            let mut all_time_data = HashMap::new();
            if let Ok(all_time_array) = result.get_array("all_time") {
                for item in all_time_array {
                    if let Some(doc) = item.as_document() {
                        if let Ok(blockchain) = doc.get_str("_id") {
                            all_time_data.insert(blockchain.to_string(), doc.clone());
                        }
                    }
                }
            }

            // Process 24h data
            let mut last_24h_data = HashMap::new();
            if let Ok(last_24h_array) = result.get_array("last_24h") {
                for item in last_24h_array {
                    if let Some(doc) = item.as_document() {
                        if let Ok(blockchain) = doc.get_str("_id") {
                            last_24h_data.insert(blockchain.to_string(), doc.clone());
                        }
                    }
                }
            }

            // Combine data for each blockchain
            let supported_blockchains = ["SOL", "BNB", "ETH", "BASE"];
            for blockchain in supported_blockchains {
                let all_time = all_time_data.get(blockchain);
                let last_24h = last_24h_data.get(blockchain);

                let total_transactions = all_time.and_then(|d| d.get_i64("total_transactions").ok()).unwrap_or(0);
                let total_volume = all_time.and_then(|d| d.get_f64("total_volume").ok()).unwrap_or(0.0);
                let successful_transactions = all_time.and_then(|d| d.get_i64("successful_transactions").ok()).unwrap_or(0);
                let unique_users_count = all_time.and_then(|d| d.get_array("unique_users").ok().map(|arr| arr.len() as i64)).unwrap_or(0);
                let total_fees = all_time.and_then(|d| d.get_f64("total_fees").ok()).unwrap_or(0.0);
                let avg_response_time = all_time.and_then(|d| d.get_f64("avg_response_time").ok()).unwrap_or(250.0);

                let transactions_24h = last_24h.and_then(|d| d.get_i64("transactions_24h").ok()).unwrap_or(0);
                let volume_24h = last_24h.and_then(|d| d.get_f64("volume_24h").ok()).unwrap_or(0.0);
                let successful_24h = last_24h.and_then(|d| d.get_i64("successful_24h").ok()).unwrap_or(0);
                let unique_users_24h = last_24h.and_then(|d| d.get_array("unique_users_24h").ok().map(|arr| arr.len() as i64)).unwrap_or(0);
                let fees_24h = last_24h.and_then(|d| d.get_f64("fees_24h").ok()).unwrap_or(0.0);

                let success_rate = if total_transactions > 0 {
                    (successful_transactions as f64 / total_transactions as f64) * 100.0
                } else {
                    0.0
                };

                let analytics_data = serde_json::json!({
                    "blockchain": blockchain,
                    "total_transactions": total_transactions,
                    "total_volume": total_volume,
                    "total_fees": total_fees,
                    "unique_users": unique_users_count,
                    "success_rate": success_rate,
                    "avg_response_time": avg_response_time,
                    "transactions_24h": transactions_24h,
                    "volume_24h": volume_24h,
                    "fees_24h": fees_24h,
                    "unique_users_24h": unique_users_24h,
                    "avg_transaction_amount": if total_transactions > 0 { total_volume / total_transactions as f64 } else { 0.0 },
                    "user_growth_24h": if unique_users_count > 0 { (unique_users_24h as f64 / unique_users_count as f64) * 100.0 } else { 0.0 }
                });

                blockchain_analytics.insert(blockchain.to_string(), analytics_data);
            }
        }

        debug!("Detailed blockchain analytics calculated for {} blockchains", blockchain_analytics.len());
        Ok(blockchain_analytics)
    }

    async fn calculate_admin_fees_collected(&self, twenty_four_hours_ago: u64) -> Result<(f64, f64), AnalyticsError> {
        let db = DbService::get_db();
        let fee_collection = db.collection::<mongodb::bson::Document>("admin_fee_transactions");

        // Calculate total fees collected in USD (completed transactions only)
        let total_fees_pipeline = vec![
            doc! {
                "$match": {
                    "status": "completed"
                }
            },
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "total_fees_native": { "$sum": "$fee_amount" },
                    "fee_token_symbol": { "$first": "$fee_token_symbol" }
                }
            }
        ];

        // Calculate 24h fees collected in USD
        let fees_24h_pipeline = vec![
            doc! {
                "$match": {
                    "status": "completed",
                    "completed_at": { "$gte": twenty_four_hours_ago as i64 }
                }
            },
            doc! {
                "$group": {
                    "_id": "$blockchain",
                    "fees_24h_native": { "$sum": "$fee_amount" },
                    "fee_token_symbol": { "$first": "$fee_token_symbol" }
                }
            }
        ];

        let total_fees_future = fee_collection.aggregate(total_fees_pipeline, None);
        let fees_24h_future = fee_collection.aggregate(fees_24h_pipeline, None);

        let (mut total_cursor, mut fees_24h_cursor) = tokio::try_join!(
            total_fees_future,
            fees_24h_future
        )?;

        let mut total_fees_usd = 0.0;
        let mut fees_24h_usd = 0.0;

        // Process total fees and convert to USD
        while let Ok(Some(result)) = total_cursor.try_next().await {
            let blockchain = result.get_str("_id").unwrap_or("Unknown");
            let total_fees_native = result.get_f64("total_fees_native").unwrap_or(0.0);
            let fee_token_symbol = result.get_str("fee_token_symbol").unwrap_or("Unknown");

            // Convert to USD using price service
            if let Ok(usd_value) = self.get_usd_value(fee_token_symbol, total_fees_native).await {
                total_fees_usd += usd_value;
                debug!("Total fees for {}: {} {} = ${:.2}", blockchain, total_fees_native, fee_token_symbol, usd_value);
            }
        }

        // Process 24h fees and convert to USD
        while let Ok(Some(result)) = fees_24h_cursor.try_next().await {
            let blockchain = result.get_str("_id").unwrap_or("Unknown");
            let fees_24h_native = result.get_f64("fees_24h_native").unwrap_or(0.0);
            let fee_token_symbol = result.get_str("fee_token_symbol").unwrap_or("Unknown");

            // Convert to USD using price service
            if let Ok(usd_value) = self.get_usd_value(fee_token_symbol, fees_24h_native).await {
                fees_24h_usd += usd_value;
                debug!("24h fees for {}: {} {} = ${:.2}", blockchain, fees_24h_native, fee_token_symbol, usd_value);
            }
        }

        // Round to 2 decimal places
        total_fees_usd = (total_fees_usd * 100.0).round() / 100.0;
        fees_24h_usd = (fees_24h_usd * 100.0).round() / 100.0;

        info!("Admin fees aggregated - Total: ${:.2}, 24h: ${:.2}", total_fees_usd, fees_24h_usd);
        Ok((total_fees_usd, fees_24h_usd))
    }

    /// Get USD value for a given amount of native token using cached price service
    async fn get_usd_value(&self, token_symbol: &str, amount: f64) -> Result<f64, AnalyticsError> {
        let cached_price_service = crate::service::cached_price_service::CachedPriceService::new();

        // Map token symbols to blockchain
        let blockchain = match token_symbol {
            "SOL" => crate::model::blockchain::Blockchain::SOL,
            "ETH" => crate::model::blockchain::Blockchain::ETH,
            "BNB" => crate::model::blockchain::Blockchain::BSC,
            "BASE" => crate::model::blockchain::Blockchain::BASE,
            _ => return Ok(0.0), // Unknown token
        };

        match cached_price_service.get_cached_price(&blockchain).await {
            Ok(price) => Ok(amount * price),
            Err(e) => {
                warn!("Failed to get USD price for {} using cached service: {}", token_symbol, e);
                // Return a fallback calculation to prevent complete failure
                let fallback_price = match token_symbol {
                    "SOL" => 100.0,
                    "ETH" => 3000.0,
                    "BNB" => 400.0,
                    "BASE" => 3000.0,
                    _ => 1.0,
                };
                warn!("Using fallback price for {}: ${:.2}", token_symbol, fallback_price);
                Ok(amount * fallback_price)
            }
        }
    }
}

impl Clone for AnalyticsController {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            jwt_secret: self.jwt_secret.clone(),
            cache: self.cache.clone(),
        }
    }
}
